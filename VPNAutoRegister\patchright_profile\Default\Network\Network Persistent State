{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["FAAAABAAAABodHRwczovL2d2dDEuY29t", false, 0], "broken_count": 1, "host": "r3---sn-npoe7nsr.gvt1.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABQAAABodHRwczovLzUycG9rZW1vbi5jYw==", false, 0], "broken_count": 1, "host": "content-autofill.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 2, "broken_until": "**********", "host": "accounts.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABQAAABodHRwczovLzUycG9rZW1vbi5jYw==", false, 0], "broken_count": 3, "broken_until": "**********", "host": "api123.136470.xyz", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 4, "broken_until": "**********", "host": "android.clients.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "broken_count": 4, "broken_until": "**********", "host": "optimizationguide-pa.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABQAAABodHRwczovLzUycG9rZW1vbi5jYw==", false, 0], "broken_count": 5, "broken_until": "**********", "host": "web2.52pokemon.cc", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABQAAABodHRwczovLzUycG9rZW1vbi5jYw==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "s2.loli.net", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400726102153864", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL2d2dDEuY29t", false, 0], "server": "https://redirector.gvt1.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL2d2dDEuY29t", false, 0], "server": "https://dl.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://android.clients.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACwAAABodHRwczovL3Bhc3N3b3Jkc2xlYWtjaGVjay1wYS5nb29nbGVhcGlzLmNvbQ==", false, 0], "server": "https://passwordsleakcheck-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovLzUycG9rZW1vbi5jYw==", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398224518251689", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovLzUycG9rZW1vbi5jYw==", false, 0], "server": "https://s2.loli.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400730119526247", "port": 443, "protocol_str": "quic"}], "anonymization": ["LAAAACcAAABodHRwczovL2NvbnRlbnQtYXV0b2ZpbGwuZ29vZ2xlYXBpcy5jb20A", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400730119529729", "port": 443, "protocol_str": "quic"}], "anonymization": ["IAAAABoAAABodHRwczovL3d3dy5nb29nbGVhcGlzLmNvbQAA", false, 0], "server": "https://www.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398224524881377", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovLzUycG9rZW1vbi5jYw==", false, 0], "server": "https://web2.52pokemon.cc", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398224527091739", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovLzUycG9rZW1vbi5jYw==", false, 0], "server": "https://api123.136470.xyz", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAISABiAgICA+P////8B": "4G"}}}