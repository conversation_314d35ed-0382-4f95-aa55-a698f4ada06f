# Pokemon订阅链接提取器

自动化脚本，用于登录Pokemon网站并提取订阅链接，包含智能弹窗处理功能。

## 功能特性

- ✅ 自动登录Pokemon网站
- ✅ 智能处理系统公告弹窗
- ✅ 提取V2rayN订阅链接
- ✅ 提取通用订阅链接
- ✅ 多重容错机制
- ✅ 详细日志记录

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 基本使用

```python
from pokemon_subscription_extractor import PokemonSubscriptionExtractor

# 创建提取器实例
extractor = PokemonSubscriptionExtractor(headless=False)

# 运行提取流程
result = extractor.run("your_username", "your_password")

# 查看结果
if result:
    for name, link in result.items():
        print(f"{name}: {link}")
```

### 2. 运行测试

```bash
python test_pokemon_extractor.py
```

测试选项：
1. 测试弹窗处理功能
2. 测试完整提取流程（需要登录）
3. 手动测试（假设已登录）
4. 运行所有测试

## 配置说明

### 修改登录信息

编辑 `pokemon_subscription_extractor.py` 文件中的主函数：

```python
def main():
    USERNAME = "your_username"  # 替换为实际用户名
    PASSWORD = "your_password"  # 替换为实际密码
```

### 修改网站URL

如果网站地址发生变化，请更新以下URL：

```python
login_url = "https://pokemon.com/login"        # 登录页面
dashboard_url = "https://pokemon.com/dashboard" # 仪表板页面
```

## 故障排除

### 1. 弹窗无法关闭
- 检查网站是否更新了弹窗结构
- 查看日志文件 `pokemon_extractor.log` 获取详细错误信息

### 2. 订阅链接提取失败
- 确认已成功登录
- 检查按钮选择器是否仍然有效
- 尝试手动测试模式

### 3. ChromeDriver问题
- 确保Chrome浏览器已安装
- 安装webdriver-manager自动管理ChromeDriver：
  ```bash
  pip install webdriver-manager
  ```

## 日志文件

脚本运行时会生成 `pokemon_extractor.log` 日志文件，包含详细的操作记录和错误信息。

## 注意事项

1. **登录凭据**: 需要有效的Pokemon网站账户
2. **网络环境**: 确保能正常访问Pokemon网站
3. **浏览器版本**: 建议使用最新版本的Chrome浏览器
4. **选择器更新**: 如果网站结构发生变化，可能需要更新元素选择器

## 技术架构

- **Selenium**: Web自动化框架
- **PyperClip**: 剪贴板操作
- **Python logging**: 日志记录系统

## 许可证

本项目仅供学习和个人使用。
