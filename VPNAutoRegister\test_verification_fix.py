#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证码填写修复测试脚本
测试优化后的验证码填写逻辑和等待时间优化
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from vpn_register_patchright import VPNRegisterPatchright
from improved_verification import ImprovedVerificationCodeExtractor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_verification_fix.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

async def test_verification_code_filling():
    """测试验证码填写功能"""
    logger.info("开始测试验证码填写修复")

    try:
        # 1. 测试验证码获取
        logger.info("测试验证码获取功能...")
        extractor = ImprovedVerificationCodeExtractor()
        accounts = extractor.get_available_accounts()

        if not accounts:
            logger.error("无可用邮箱账户")
            return False

        test_email = accounts[0]['email']
        logger.info(f"使用测试邮箱: {test_email}")

        # 2. 测试浏览器自动化（仅到验证码填写步骤）
        logger.info("测试浏览器自动化...")
        register = VPNRegisterPatchright()

        # 模拟验证码填写测试
        result = await test_browser_automation(register, test_email)

        if result:
            logger.info("验证码填写修复测试成功")
            return True
        else:
            logger.error("验证码填写修复测试失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中出错: {e}")
        return False

async def test_browser_automation(register, email):
    """测试浏览器自动化部分"""
    try:
        from patchright.async_api import async_playwright
        
        async with async_playwright() as p:
            # 使用优化的浏览器配置
            browser = await p.chromium.launch_persistent_context(
                user_data_dir="./patchright_profile",
                headless=False,  # 显示浏览器以便观察
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--no-sandbox',
                    '--disable-web-security'
                ]
            )
            
            try:
                page = browser.pages[0] if browser.pages else await browser.new_page()
                
                # 访问注册页面
                logger.info("访问注册页面...")
                await page.goto(register.target_url, wait_until='domcontentloaded')

                # 测试优化的页面加载等待
                try:
                    await page.wait_for_selector("input[placeholder*='邮箱']", timeout=10000)
                    logger.info("页面关键元素已加载")
                except:
                    logger.warning("关键元素加载超时")
                    await page.wait_for_timeout(3000)

                # 测试邮箱填写
                email_prefix = email.split('@')[0]
                logger.info(f"填写邮箱前缀: {email_prefix}")

                email_input = await page.wait_for_selector("input[type='text']:first-of-type", timeout=10000)
                await email_input.scroll_into_view_if_needed()
                await email_input.click()
                await page.wait_for_timeout(300)
                await email_input.clear()
                await email_input.fill(email_prefix)
                logger.info("邮箱前缀填写完成")
                
                # 测试验证码输入框定位
                logger.info("测试验证码输入框定位...")
                code_selectors = [
                    "#input-26",  # 精确的验证码输入框ID
                    "input[placeholder*='验证码']",
                    "input[placeholder*='请输入验证码']",
                    "input[type='text']:last-of-type",
                    ".v-text-field:last-of-type input"
                ]

                code_input = None
                for selector in code_selectors:
                    try:
                        code_input = await page.wait_for_selector(selector, timeout=5000)
                        if code_input:
                            is_disabled = await code_input.is_disabled()
                            is_readonly = await code_input.get_attribute("readonly")

                            if not is_disabled and not is_readonly:
                                logger.info(f"找到可用的验证码输入框: {selector}")
                                break
                            else:
                                logger.info(f"验证码输入框不可用: {selector}")
                                code_input = None
                    except:
                        continue

                if code_input:
                    # 测试验证码填写（使用模拟验证码）
                    test_code = "123456"
                    logger.info(f"测试验证码填写: {test_code}")

                    await code_input.scroll_into_view_if_needed()
                    await code_input.click()
                    await page.wait_for_timeout(300)

                    await code_input.clear()
                    await page.wait_for_timeout(200)
                    await code_input.fill(test_code)
                    await page.wait_for_timeout(500)

                    # 验证输入
                    input_value = await code_input.input_value()
                    if input_value == test_code:
                        logger.info("验证码填写测试成功")
                        return True
                    else:
                        logger.warning(f"验证码填写测试失败，期望: {test_code}, 实际: {input_value}")
                        return False
                else:
                    logger.error("未找到验证码输入框")
                    return False
                    
            finally:
                logger.info("等待5秒后关闭浏览器...")
                await page.wait_for_timeout(5000)
                await browser.close()
                
    except Exception as e:
        logger.error(f"浏览器自动化测试失败: {e}")
        return False

async def main():
    """主函数"""
    logger.info("VPN自动注册 - 验证码填写修复测试")
    logger.info("=" * 50)

    success = await test_verification_code_filling()

    logger.info("=" * 50)
    if success:
        logger.info("测试完成 - 修复成功")
    else:
        logger.info("测试完成 - 需要进一步修复")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
