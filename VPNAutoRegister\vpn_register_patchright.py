#!/usr/bin/env python3
"""
VPN自动注册脚本 - Patchright版本
使用Patchright绕过反自动化检测
"""

import asyncio
import json
import random
import string
import time
import logging
from datetime import datetime
import requests
from patchright.async_api import async_playwright

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'vpn_register_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class VPNRegisterPatchright:
    def __init__(self):
        self.outlook_api_base = "http://localhost:8010"
        self.target_url = "https://web2.52pokemon.cc/register"
        
    async def get_available_email(self):
        """从OutlookManager获取可用邮箱"""
        try:
            # 方案1: 直接读取accounts.json文件
            accounts_file = "D:/Dev/Email/OutlookManager/accounts.json"
            with open(accounts_file, 'r', encoding='utf-8') as f:
                accounts = json.load(f)
            
            if accounts:
                email = list(accounts.keys())[0]
                logger.info(f"从文件获取到可用邮箱: {email}")
                return email
            else:
                logger.error("accounts.json中没有邮箱账户")
                return None
                
        except Exception as e:
            logger.error(f"读取accounts.json失败: {e}")
            # 方案2: 尝试API
            try:
                async with httpx.AsyncClient(proxies={}) as client:
                    headers = {"Authorization": "Bearer admin123"}
                    response = await client.get(f"{self.outlook_api_base}/accounts", headers=headers)
                    
                    if response.status_code == 200:
                        accounts = response.json()
                        if accounts:
                            email = accounts[0]['email']
                            logger.info(f"通过API获取到邮箱: {email}")
                            return email
                    else:
                        logger.error(f"API获取邮箱失败: {response.status_code}")
            except Exception as api_error:
                logger.error(f"API调用失败: {api_error}")
            
            return None

    def test_outlook_api_connection(self):
        """测试OutlookManager API连接"""
        try:
            headers = {"Authorization": "Bearer admin123"}
            
            # 测试认证配置
            response = requests.get(
                f"{self.outlook_api_base}/auth/config",
                headers=headers
            )
            
            if response.status_code == 200:
                config = response.json()
                logger.info(f"OutlookManager API连接成功: {config}")
                return True
            else:
                logger.error(f"OutlookManager API连接失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            logger.error(f"OutlookManager API连接异常: {e}")
            return False

    def get_verification_code_improved(self, target_email: str, max_wait: int = 60) -> str:
        """改进的验证码获取方法 - 确保获取最新邮件和正确字段"""
        logger.info(f"🚀 开始获取验证码: {target_email}")
        
        start_time = time.time()
        while time.time() - start_time < max_wait:
            try:
                # 1. 尝试获取最新邮件
                logger.info("📧 尝试获取最新验证码邮件...")
                latest_email = self.get_latest_pokemon_email(target_email)
                
                if latest_email:
                    # 2. 从邮件中提取验证码
                    verification_code = self.extract_verification_code_from_email(latest_email)
                    if verification_code:
                        logger.info(f"成功提取验证码: {verification_code}")
                        return verification_code
                
                # 等待后重试
                logger.info("⏳ 未找到验证码，3秒后重试...")
                time.sleep(3)
                
            except Exception as e:
                logger.error(f"获取验证码异常: {e}")
                time.sleep(3)
        
        logger.error("❌ 获取验证码超时")
        return None
    
    def get_latest_pokemon_email(self, email: str) -> dict:
        """获取最新的宝可梦验证码邮件"""
        try:
            # 搜索垃圾邮件文件夹中的验证码邮件
            logger.info("🔍 搜索垃圾邮件文件夹中的验证码邮件...")
            response = requests.get(
                f"{self.outlook_api_base}/emails/{email}",
                params={"folder": "junk", "page": 1, "page_size": 20, "force_refresh": True},
                headers={"Authorization": "Bearer admin123"}
            )
            
            if response.status_code != 200:
                logger.error(f"获取垃圾邮件失败: {response.status_code}")
                return None
            
            emails_data = response.json()
            emails = emails_data.get('emails', [])
            logger.info(f"垃圾邮件文件夹中有 {len(emails)} 封邮件")
            
            # 查找宝可梦验证码邮件
            pokemon_emails = []
            for email_item in emails:
                subject = email_item.get('subject', '')
                if any(keyword in subject.lower() for keyword in ['宝可梦', 'pokemon', '验证码']):
                    pokemon_emails.append(email_item)
                    logger.info(f"找到验证码邮件: {subject} ({email_item.get('date', 'N/A')})")
            
            if not pokemon_emails:
                logger.warning("垃圾邮件文件夹中未找到宝可梦验证码邮件")
                return None
            
            # 获取最新的验证码邮件详情
            latest_pokemon = pokemon_emails[0]  # 假设API返回的邮件已按时间排序
            message_id = latest_pokemon.get('message_id')
            
            if not message_id:
                logger.error("邮件缺少message_id")
                return None
            
            # 获取邮件详细内容
            detail_response = requests.get(
                f"{self.outlook_api_base}/emails/{email}/{message_id}",
                headers={"Authorization": "Bearer admin123"}
            )
            
            if detail_response.status_code == 200:
                email_detail = detail_response.json()
                logger.info(f"成功获取邮件详情: {email_detail.get('subject', 'N/A')}")
                return email_detail
            else:
                logger.error(f"获取邮件详情失败: {detail_response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"搜索验证码邮件异常: {e}")
            return None
    
    def extract_verification_code_from_email(self, email_detail: dict) -> str:
        """从邮件详情中提取验证码"""
        try:
            subject = email_detail.get('subject', '')
            body_plain = email_detail.get('body_plain', '') or ''
            body_html = email_detail.get('body_html', '') or ''
            
            logger.info(f"邮件主题: {subject}")
            logger.info(f"纯文本内容长度: {len(body_plain)}")
            logger.info(f"HTML内容长度: {len(body_html)}")
            
            # 合并所有文本内容
            content = f"{subject} {body_plain} {body_html}"
            
            return self.extract_verification_code_patterns(content)
            
        except Exception as e:
            logger.error(f"从邮件提取验证码异常: {e}")
            return None
    
    def extract_verification_code_patterns(self, content: str) -> str:
        """使用多种模式提取验证码"""
        import re
        
        # 模式1: 验证码: 123456 或 验证码：123456
        pattern1 = re.findall(r'验证码[：:]\s*(\d{4,6})', content, re.IGNORECASE)
        if pattern1:
            logger.info(f"✅ 模式1匹配: {pattern1[0]}")
            return pattern1[0]
        
        # 模式2: verification code: 123456
        pattern2 = re.findall(r'verification\s+code[：:]\s*(\d{4,6})', content, re.IGNORECASE)
        if pattern2:
            logger.info(f"✅ 模式2匹配: {pattern2[0]}")
            return pattern2[0]
        
        # 模式3: 您的验证码是 123456
        pattern3 = re.findall(r'您的验证码是\s*(\d{4,6})', content, re.IGNORECASE)
        if pattern3:
            logger.info(f"✅ 模式3匹配: {pattern3[0]}")
            return pattern3[0]
        
        # 模式4: 宝可梦特定格式 - 6位数字独立出现（最常见）
        pattern4 = re.findall(r'\b(\d{6})\b', content)
        if pattern4:
            # 过滤掉明显不是验证码的数字
            filtered_codes = []
            for code in pattern4:
                if not code.startswith(('19', '20', '80', '443', '8080', '000', '111', '222', '333', '444', '555', '666', '777', '888', '999')):
                    filtered_codes.append(code)
            
            if filtered_codes:
                logger.info(f"✅ 模式4匹配: {filtered_codes[0]} (从 {pattern4} 中筛选)")
                return filtered_codes[0]
        
        # 模式5: 通用4-6位数字（作为备选）
        pattern5 = re.findall(r'\b\d{4,6}\b', content)
        if pattern5:
            filtered_codes = []
            for code in pattern5:
                if not code.startswith(('19', '20', '80', '443', '8080', '000')):
                    filtered_codes.append(code)
            
            if filtered_codes:
                logger.info(f"✅ 模式5匹配: {filtered_codes[0]} (从 {pattern5} 中筛选)")
                return filtered_codes[0]
        
        logger.warning("❌ 所有模式都未匹配到验证码")
        return None

    async def get_verification_code(self, email, max_wait=60):
        """获取验证码 - 使用正确的API调用方式"""
        logger.info(f"等待验证码邮件: {email}")
        
        # 首先测试API连接
        if not self.test_outlook_api_connection():
            logger.error("OutlookManager API连接失败，无法获取验证码")
            return None
        
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                headers = {"Authorization": "Bearer admin123"}
                
                # 第一步：获取邮件列表（收件箱）
                response = requests.get(
                    f"{self.outlook_api_base}/emails/{email}",
                    params={"folder": "inbox", "page": 1, "page_size": 10, "force_refresh": True},
                    headers=headers
                )
                
                if response.status_code == 200:
                        emails_data = response.json()
                        if emails_data.get('emails'):
                            # 遍历最新的邮件
                            for email_item in emails_data['emails']:
                                subject = email_item.get('subject', '')
                                message_id = email_item.get('message_id', '')
                                
                                # 检查邮件主题是否包含验证码相关关键词
                                if any(keyword in subject.lower() for keyword in ['验证码', 'verification', 'code', '宝可梦', 'pokemon']):
                                    logger.info(f"找到可能的验证码邮件: {subject}")
                                    
                                    # 第二步：获取邮件详细内容
                                    detail_response = await client.get(
                                        f"{self.outlook_api_base}/emails/{email}/{message_id}",
                                        headers=headers
                                    )
                                    
                                    if detail_response.status_code == 200:
                                        email_detail = detail_response.json()
                                        body_plain = email_detail.get('body_plain', '')
                                        body_html = email_detail.get('body_html', '')
                                        
                                        # 合并所有文本内容进行搜索
                                        content = f"{subject} {body_plain} {body_html}"
                                        logger.info(f"邮件内容预览: {content[:200]}...")
                                        
                                        # 多种验证码提取模式
                                        import re
                                        
                                        # 模式1: 验证码: 123456 或 验证码：123456
                                        pattern1 = re.findall(r'验证码[：:]\s*(\d{4,6})', content, re.IGNORECASE)
                                        if pattern1:
                                            code = pattern1[0]
                                            logger.info(f"模式1提取验证码: {code}")
                                            return code
                                        
                                        # 模式2: verification code: 123456
                                        pattern2 = re.findall(r'verification\s+code[：:]\s*(\d{4,6})', content, re.IGNORECASE)
                                        if pattern2:
                                            code = pattern2[0]
                                            logger.info(f"模式2提取验证码: {code}")
                                            return code
                                        
                                        # 模式3: 您的验证码是 123456
                                        pattern3 = re.findall(r'您的验证码是\s*(\d{4,6})', content, re.IGNORECASE)
                                        if pattern3:
                                            code = pattern3[0]
                                            logger.info(f"模式3提取验证码: {code}")
                                            return code
                                        
                                        # 模式4: 通用4-6位数字（作为备选）
                                        pattern4 = re.findall(r'\b\d{4,6}\b', content)
                                        if pattern4:
                                            # 过滤掉明显不是验证码的数字（如年份、端口号等）
                                            filtered_codes = [code for code in pattern4 if not code.startswith(('19', '20', '80', '443', '8080'))]
                                            if filtered_codes:
                                                code = filtered_codes[0]
                                                logger.info(f"模式4提取验证码: {code}")
                                                return code
                                        
                                        logger.warning(f"邮件中未找到验证码: {subject}")
                                    else:
                                        logger.warning(f"获取邮件详情失败: {detail_response.status_code}")
                else:
                    logger.warning(f"获取收件箱邮件失败: {response.status_code} - {response.text}")
                    
                    # 如果收件箱没找到，检查垃圾邮件文件夹
                    logger.info("检查垃圾邮件文件夹...")
                    junk_response = await client.get(
                        f"{self.outlook_api_base}/emails/{email}",
                        params={"folder": "junk", "page": 1, "page_size": 10},
                        headers=headers
                    )
                    
                    if junk_response.status_code == 200:
                        junk_data = junk_response.json()
                        if junk_data.get('emails'):
                            # 遍历垃圾邮件中的邮件
                            for email_item in junk_data['emails']:
                                subject = email_item.get('subject', '')
                                message_id = email_item.get('message_id', '')
                                
                                # 检查邮件主题是否包含验证码相关关键词
                                if any(keyword in subject.lower() for keyword in ['验证码', 'verification', 'code', '宝可梦', 'pokemon']):
                                    logger.info(f"在垃圾邮件中找到可能的验证码邮件: {subject}")
                                    
                                    # 获取邮件详细内容
                                    detail_response = await client.get(
                                        f"{self.outlook_api_base}/emails/{email}/{message_id}",
                                        headers=headers
                                    )
                                    
                                    if detail_response.status_code == 200:
                                        email_detail = detail_response.json()
                                        body_plain = email_detail.get('body_plain', '')
                                        body_html = email_detail.get('body_html', '')
                                        
                                        # 合并所有文本内容进行搜索
                                        content = f"{subject} {body_plain} {body_html}"
                                        logger.info(f"垃圾邮件内容预览: {content[:200]}...")
                                        
                                        # 使用相同的验证码提取逻辑
                                        import re
                                        
                                        # 模式1: 验证码: 123456 或 验证码：123456
                                        pattern1 = re.findall(r'验证码[：:]\s*(\d{4,6})', content, re.IGNORECASE)
                                        if pattern1:
                                            code = pattern1[0]
                                            logger.info(f"从垃圾邮件模式1提取验证码: {code}")
                                            return code
                                        
                                        # 模式2: verification code: 123456
                                        pattern2 = re.findall(r'verification\s+code[：:]\s*(\d{4,6})', content, re.IGNORECASE)
                                        if pattern2:
                                            code = pattern2[0]
                                            logger.info(f"从垃圾邮件模式2提取验证码: {code}")
                                            return code
                                        
                                        # 模式3: 您的验证码是 123456
                                        pattern3 = re.findall(r'您的验证码是\s*(\d{4,6})', content, re.IGNORECASE)
                                        if pattern3:
                                            code = pattern3[0]
                                            logger.info(f"从垃圾邮件模式3提取验证码: {code}")
                                            return code
                                        
                                        # 模式4: 通用4-6位数字（作为备选）
                                        pattern4 = re.findall(r'\b\d{4,6}\b', content)
                                        if pattern4:
                                            # 过滤掉明显不是验证码的数字
                                            filtered_codes = [code for code in pattern4 if not code.startswith(('19', '20', '80', '443', '8080'))]
                                            if filtered_codes:
                                                code = filtered_codes[0]
                                                logger.info(f"从垃圾邮件模式4提取验证码: {code}")
                                                return code
                    else:
                        logger.warning(f"获取垃圾邮件失败: {junk_response.status_code}")
                
                logger.info("收件箱和垃圾邮件文件夹都未找到验证码，3秒后重试...")
                await asyncio.sleep(3)
                
            except Exception as e:
                logger.error(f"获取验证码时出错: {e}")
                await asyncio.sleep(3)
        
        logger.error("获取验证码超时")
        return None

    def generate_password(self, length=12):
        """生成安全密码"""
        characters = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(random.choice(characters) for _ in range(length))
        return password

    async def register_single_account(self):
        """注册单个VPN账户 - 使用Patchright"""
        logger.info("开始VPN账户注册流程")
        
        # 1. 获取可用邮箱
        email = await self.get_available_email()
        if not email:
            return {"success": False, "error": "无法获取邮箱"}
        
        # 2. 生成密码
        password = self.generate_password()
        
        # 3. 启动Patchright浏览器
        logger.info("启动Patchright浏览器...")
        
        async with async_playwright() as p:
            # 使用Patchright的最佳实践配置
            browser = await p.chromium.launch_persistent_context(
                user_data_dir="./patchright_profile",
                channel="chrome",  # 使用真实Chrome而不是Chromium
                headless=False,
                no_viewport=True,
                # 不添加自定义headers或user_agent以避免检测
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--no-sandbox',
                    '--disable-web-security'
                ]
            )
            
            try:
                page = browser.pages[0] if browser.pages else await browser.new_page()
                
                # 访问注册页面
                logger.info(f"访问注册页面: {self.target_url}")
                await page.goto(self.target_url, wait_until='domcontentloaded')

                # 优化页面加载等待 - 等待关键元素而不是固定时间
                try:
                    await page.wait_for_selector("input[placeholder*='邮箱']", timeout=10000)
                    logger.info("页面关键元素已加载")
                except:
                    logger.warning("关键元素加载超时，继续执行")
                    await page.wait_for_timeout(3000)  # 降低固定等待时间

                current_url = page.url
                logger.info(f"当前页面URL: {current_url}")
                
                # 检查是否被重定向
                if 'login' in current_url:
                    logger.error("页面被重定向到登录页面")
                    return {"success": False, "error": "页面被重定向，Patchright可能也被检测"}
                elif 'error-page' in current_url:
                    logger.error("页面跳转到错误页面")
                    return {"success": False, "error": "页面跳转到错误页面"}
                elif 'register' not in current_url:
                    logger.error("页面未正确加载注册页面")
                    return {"success": False, "error": "页面加载异常"}
                
                logger.info("成功绕过反自动化检测！")
                
                # 优化Vue.js加载等待 - 等待表单元素而不是固定时间
                try:
                    await page.wait_for_selector("form", timeout=8000)
                    await page.wait_for_selector("input[type='text']", timeout=5000)
                    logger.info("Vue.js表单已加载")
                except:
                    logger.warning("Vue.js加载检测超时，继续执行")
                    await page.wait_for_timeout(3000)
                
                # 填写邮箱地址
                email_prefix = email.split('@')[0]
                logger.info(f"填写邮箱前缀: {email_prefix}")
                
                # 找到邮箱输入框（第一个输入框）
                try:
                    email_input = await page.wait_for_selector("input[type='text']:first-of-type", timeout=10000)
                    await email_input.scroll_into_view_if_needed()
                    await email_input.click()
                    await page.wait_for_timeout(300)  # 减少等待时间
                    await email_input.fill("")  # 清空输入框
                    await email_input.fill(email_prefix)
                    logger.info("邮箱前缀填写完成")
                    await page.wait_for_timeout(500)  # 减少等待时间
                except Exception as e:
                    logger.error(f"填写邮箱前缀失败: {e}")
                    return {"success": False, "error": "填写邮箱前缀失败"}
                
                # 选择邮箱后缀 - 确保选择@outlook.com
                logger.info("选择邮箱后缀: @outlook.com")
                try:
                    # 点击邮箱后缀下拉框
                    suffix_dropdown = await page.wait_for_selector("div.v-select", timeout=5000)
                    await suffix_dropdown.click()
                    await page.wait_for_timeout(1000)
                    
                    # 选择@outlook.com选项
                    outlook_option = await page.wait_for_selector("div[role='option']:has-text('@outlook.com')", timeout=5000)
                    await outlook_option.click()
                    logger.info("邮箱后缀选择完成: @outlook.com")
                    await page.wait_for_timeout(1000)
                except Exception as e:
                    logger.warning(f"邮箱后缀选择失败，可能已经是默认值: {e}")
                    # 继续执行，可能默认就是outlook.com
                
                # 填写密码
                logger.info("填写密码")
                try:
                    password_inputs = await page.query_selector_all("input[type='password']")
                    
                    if len(password_inputs) >= 2:
                        # 填写密码
                        await password_inputs[0].scroll_into_view_if_needed()
                        await password_inputs[0].click()
                        await page.wait_for_timeout(500)
                        await password_inputs[0].fill("")  # 清空
                        await password_inputs[0].fill(password)
                        
                        # 确认密码
                        await password_inputs[1].scroll_into_view_if_needed()
                        await password_inputs[1].click()
                        await page.wait_for_timeout(500)
                        await password_inputs[1].fill("")  # 清空
                        await password_inputs[1].fill(password)
                        
                        logger.info("密码填写完成")
                    else:
                        logger.error("未找到足够的密码输入框")
                        return {"success": False, "error": "未找到密码输入框"}
                except Exception as e:
                    logger.error(f"填写密码失败: {e}")
                    return {"success": False, "error": "填写密码失败"}
                
                # 处理邀请码（可选字段）
                logger.info("检查邀请码输入框")
                try:
                    invite_selectors = [
                        "input[placeholder*='邀请码']",
                        "input[placeholder*='邀请']",
                        "input[type='text']:nth-of-type(3)"  # 第三个文本输入框通常是邀请码
                    ]
                    
                    invite_input = None
                    for selector in invite_selectors:
                        try:
                            invite_input = await page.wait_for_selector(selector, timeout=3000)
                            if invite_input:
                                logger.info(f"找到邀请码输入框: {selector}")
                                break
                        except:
                            continue
                    
                    if invite_input:
                        # 邀请码是可选的，可以留空或填写默认值
                        logger.info("邀请码输入框存在，但保持为空（可选字段）")
                        # 如果需要填写邀请码，可以在这里添加逻辑
                        # await invite_input.fill("your_invite_code")
                    else:
                        logger.info("未找到邀请码输入框，跳过")
                except Exception as e:
                    logger.info(f"邀请码处理跳过: {e}")
                
                await page.wait_for_timeout(2000)
                
                # 检查是否已经有验证码发送倒计时
                logger.info("检查验证码发送状态")
                countdown_exists = False
                try:
                    countdown_button = await page.query_selector("button:has-text('秒后重试')")
                    if countdown_button:
                        countdown_text = await countdown_button.text_content()
                        logger.info(f"验证码已发送，倒计时状态: {countdown_text}")
                        countdown_exists = True
                except:
                    pass
                
                # 如果没有倒计时，则发送验证码
                if not countdown_exists:
                    logger.info("点击发送验证码")
                    send_code_selectors = [
                        "button:has-text('发送验证码')",
                        "button[type='button']:has-text('发送')",
                        ".v-btn:has-text('发送验证码')",
                        "button:has-text('获取验证码')"
                    ]
                    
                    send_button = None
                    for selector in send_code_selectors:
                        try:
                            send_button = await page.wait_for_selector(selector, timeout=3000)
                            if send_button:
                                # 检查按钮是否可点击
                                is_disabled = await send_button.is_disabled()
                                if not is_disabled:
                                    logger.info(f"找到发送验证码按钮: {selector}")
                                    break
                        except:
                            continue
                    
                    if send_button:
                        await send_button.scroll_into_view_if_needed()
                        await send_button.click()
                        await page.wait_for_timeout(3000)
                        logger.info("验证码已发送，开始获取验证码...")
                    else:
                        logger.error("未找到可用的发送验证码按钮")
                        return {"success": False, "error": "未找到发送验证码按钮"}
                else:
                    logger.info("验证码已经发送，直接获取验证码")
                
                # 获取验证码 - 使用改进的方法
                verification_code = self.get_verification_code_improved(email, max_wait=90)
                if not verification_code:
                    return {"success": False, "error": "获取验证码失败"}
                
                # 填写验证码
                logger.info(f"填写验证码: {verification_code}")
                code_selectors = [
                    "#input-26",  # 精确的验证码输入框ID
                    "input[placeholder*='验证码']",
                    "input[placeholder*='请输入验证码']",
                    "input[type='text']:last-of-type",
                    ".v-text-field:last-of-type input"
                ]
                
                code_input = None
                for selector in code_selectors:
                    try:
                        code_input = await page.wait_for_selector(selector, timeout=5000)
                        if code_input:
                            # 检查输入框是否可用
                            is_disabled = await code_input.is_disabled()
                            is_readonly = await code_input.get_attribute("readonly")
                            
                            # 检查元素类型
                            tag_name = await code_input.evaluate("element => element.tagName.toLowerCase()")

                            if not is_disabled and not is_readonly and tag_name == "input":
                                logger.info(f"找到可用的验证码输入框: {selector}")
                                break
                            else:
                                logger.info(f"验证码输入框不可用: {selector} (disabled: {is_disabled}, readonly: {is_readonly}, tag: {tag_name})")
                                code_input = None
                    except:
                        continue
                
                if code_input:
                    # 优化的验证码填写流程
                    await code_input.scroll_into_view_if_needed()
                    await code_input.click()
                    await page.wait_for_timeout(300)  # 减少等待时间

                    # 使用更可靠的清空和填写方法
                    await code_input.fill("")  # 清空输入框
                    await page.wait_for_timeout(200)
                    await code_input.fill(verification_code)
                    await page.wait_for_timeout(500)  # 减少等待时间

                    # 验证输入是否成功
                    try:
                        input_value = await code_input.input_value()
                        if input_value == verification_code:
                            logger.info("验证码填写完成")
                        else:
                            logger.warning(f"验证码填写可能失败，期望: {verification_code}, 实际: {input_value}")
                            # 使用更强力的重试方法
                            await code_input.click()
                            await page.keyboard.press("Control+a")  # 全选
                            await page.keyboard.press("Delete")
                            await page.wait_for_timeout(200)
                            await code_input.type(verification_code, delay=50)  # 逐字符输入
                            await page.wait_for_timeout(500)
                    except Exception as e:
                        logger.warning(f"无法验证输入值: {e}，但验证码已填写")
                        # 继续执行，因为验证码可能已经成功填写
                else:
                    logger.error("未找到可用的验证码输入框")
                    return {"success": False, "error": "未找到验证码输入框"}
                
                # 提交注册表单
                logger.info("提交注册表单")
                submit_selectors = [
                    "body > div:nth-of-type(1) > div > div > div:nth-of-type(2) > div:nth-of-type(2) > div > div > div > div > div > div:nth-of-type(2) > form > button",  # 精确选择器
                    "form > button",  # 表单中的按钮
                    "button:has-text('创建账号')",
                    "button:has-text('创建')",
                    "button[type='submit']",
                    ".v-btn:has-text('创建账号')",
                    ".v-btn:has-text('注册')"
                ]
                
                submit_button = None
                for selector in submit_selectors:
                    try:
                        submit_button = await page.wait_for_selector(selector, timeout=3000)
                        if submit_button:
                            # 检查按钮是否可用
                            is_disabled = await submit_button.is_disabled()
                            is_visible = await submit_button.is_visible()

                            if not is_disabled and is_visible:
                                logger.info(f"找到可用的提交按钮: {selector}")
                                break
                            else:
                                logger.info(f"提交按钮不可用: {selector} (disabled: {is_disabled}, visible: {is_visible})")
                                submit_button = None
                    except Exception as e:
                        logger.debug(f"选择器 {selector} 失败: {e}")
                        continue

                if submit_button:
                    logger.info("准备点击创建账户按钮...")
                    await submit_button.scroll_into_view_if_needed()
                    await page.wait_for_timeout(500)  # 等待滚动完成

                    # 尝试点击按钮
                    try:
                        await submit_button.click()
                        logger.info("成功点击创建账户按钮")
                    except Exception as e:
                        logger.warning(f"直接点击失败，尝试JavaScript点击: {e}")
                        await page.evaluate("arguments[0].click()", submit_button)
                        logger.info("使用JavaScript成功点击创建账户按钮")

                    await page.wait_for_timeout(8000)  # 等待注册处理
                else:
                    logger.error("未找到提交按钮")
                    # 尝试获取页面上所有按钮进行调试
                    try:
                        all_buttons = await page.query_selector_all("button")
                        logger.info(f"页面上共有 {len(all_buttons)} 个按钮")
                        for i, btn in enumerate(all_buttons[:5]):  # 只显示前5个
                            text = await btn.text_content()
                            logger.info(f"按钮 {i+1}: '{text}'")
                    except:
                        pass
                    return {"success": False, "error": "未找到提交按钮"}
                
                # 检查注册结果
                current_url = page.url
                logger.info(f"注册后页面URL: {current_url}")
                
                if 'dashboard' in current_url or 'success' in current_url or ('register' not in current_url and 'login' not in current_url and 'error' not in current_url):
                    logger.info("注册成功！")
                    
                    result = {
                        "success": True,
                        "email": email,
                        "password": password,
                        "subscription_link": None,
                        "registered_at": datetime.now().isoformat()
                    }
                    
                    # 保存结果
                    with open('vpn_accounts.json', 'a', encoding='utf-8') as f:
                        f.write(json.dumps(result, ensure_ascii=False) + '\n')
                    
                    return result
                else:
                    logger.error("注册可能失败，页面未跳转到预期位置")
                    return {"success": False, "error": "注册失败，页面未正确跳转"}
                    
            except Exception as e:
                logger.error(f"注册过程中出错: {e}")
                return {"success": False, "error": f"注册过程出错: {str(e)}"}
            
            finally:
                # 保持浏览器打开一段时间以便查看结果
                logger.info("等待10秒后关闭浏览器...")
                await page.wait_for_timeout(10000)
                await browser.close()

async def main():
    """主函数"""
    register = VPNRegisterPatchright()
    result = await register.register_single_account()
    
    if result["success"]:
        print("注册成功！")
        print(f"邮箱: {result['email']}")
        print(f"密码: {result['password']}")
        if result.get('subscription_link'):
            print(f"订阅链接: {result['subscription_link']}")
    else:
        print(f"注册失败: {result['error']}")

if __name__ == "__main__":
    asyncio.run(main())