#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pokemon订阅链接提取器
自动登录Pokemon网站并提取订阅链接，包含弹窗处理逻辑
"""

import time
import pyperclip
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging
import json
from datetime import datetime
from typing import Dict, Optional, Tuple, List
from enum import Enum

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pokemon_extractor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PokemonSubscriptionExtractor:
    def __init__(self, headless=False):
        """初始化浏览器"""
        self.driver = None
        self.wait = None
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """设置Chrome浏览器"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            logger.info("浏览器初始化成功")
        except Exception as e:
            logger.error(f"浏览器初始化失败: {e}")
            raise
    
    def handle_popup(self):
        """处理系统公告弹窗"""
        try:
            # 检查是否存在弹窗
            popup_indicators = [
                "//div[contains(text(), '系统公告')]",  # 系统公告标题
                "//div[contains(@class, 'v-dialog')]",  # Vue对话框
                "//div[contains(@class, 'v-overlay')]"  # Vue覆盖层
            ]

            popup_found = False
            for indicator in popup_indicators:
                try:
                    popup_element = self.driver.find_element(By.XPATH, indicator)
                    if popup_element.is_displayed():
                        popup_found = True
                        logger.info("检测到弹窗")
                        break
                except NoSuchElementException:
                    continue

            if not popup_found:
                logger.info("未检测到弹窗")
                return True

            # 等待弹窗完全加载
            time.sleep(1)

            # 查找关闭按钮的多种选择器
            close_button_selectors = [
                # 基于实际观察到的选择器
                "body > div:nth-of-type(2) > div:nth-of-type(1) > div:nth-of-type(2) > div > div:nth-of-type(4) > button",
                # 通用选择器
                "//button[text()='关闭']",
                "//button[contains(text(), '关闭')]",
                "//button[contains(@class, 'v-btn') and contains(@class, 'text-primary')]",
                "button.v-btn.text-primary",
                "[class*='v-btn'][class*='text-primary']",
                # 更宽泛的选择器
                "//div[contains(@class, 'v-dialog')]//button[contains(@class, 'v-btn')]",
                "//div[contains(@class, 'v-overlay')]//button"
            ]

            for selector in close_button_selectors:
                try:
                    if selector.startswith('//'):
                        # XPath选择器
                        close_button = WebDriverWait(self.driver, 3).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        # CSS选择器
                        close_button = WebDriverWait(self.driver, 3).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )

                    # 滚动到按钮位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", close_button)
                    time.sleep(0.5)

                    # 点击关闭按钮
                    close_button.click()
                    logger.info(f"成功点击关闭按钮: {selector}")

                    # 等待弹窗关闭
                    time.sleep(2)

                    # 验证弹窗是否已关闭
                    try:
                        popup_still_exists = self.driver.find_element(By.XPATH, "//div[contains(text(), '系统公告')]")
                        if popup_still_exists.is_displayed():
                            logger.warning("弹窗仍然存在，尝试其他方法")
                            continue
                    except NoSuchElementException:
                        logger.info("弹窗已成功关闭")
                        return True

                    return True

                except TimeoutException:
                    logger.debug(f"选择器超时: {selector}")
                    continue
                except Exception as e:
                    logger.warning(f"尝试选择器 {selector} 时出错: {e}")
                    continue

            # 如果所有选择器都失败，尝试按ESC键
            try:
                logger.info("尝试按ESC键关闭弹窗")
                from selenium.webdriver.common.keys import Keys
                self.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.ESCAPE)
                time.sleep(2)
                return True
            except Exception as e:
                logger.warning(f"按ESC键失败: {e}")

            logger.warning("所有关闭弹窗的方法都失败了")
            return False

        except Exception as e:
            logger.error(f"处理弹窗时出错: {e}")
            return False
    
    def login(self, username, password):
        """登录Pokemon网站"""
        try:
            # 访问登录页面
            login_url = "https://pokemon.com/login"  # 请替换为实际的登录URL
            self.driver.get(login_url)
            logger.info(f"访问登录页面: {login_url}")
            
            # 处理可能出现的弹窗
            time.sleep(2)
            self.handle_popup()
            
            # 查找并填写用户名
            username_field = self.wait.until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            username_field.clear()
            username_field.send_keys(username)
            logger.info("已输入用户名")
            
            # 查找并填写密码
            password_field = self.driver.find_element(By.NAME, "password")
            password_field.clear()
            password_field.send_keys(password)
            logger.info("已输入密码")
            
            # 点击登录按钮
            login_button = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
            login_button.click()
            logger.info("已点击登录按钮")
            
            # 等待登录完成
            self.wait.until(EC.url_changes(login_url))
            logger.info("登录成功")
            
            # 登录后可能还有弹窗，再次处理
            time.sleep(2)
            self.handle_popup()
            
            return True
            
        except Exception as e:
            logger.error(f"登录失败: {e}")
            return False
    
    def navigate_to_dashboard(self):
        """导航到仪表板页面"""
        try:
            # 访问仪表板页面
            dashboard_url = "https://pokemon.com/dashboard"  # 请替换为实际的仪表板URL
            self.driver.get(dashboard_url)
            logger.info(f"访问仪表板页面: {dashboard_url}")
            
            # 处理可能出现的弹窗
            time.sleep(2)
            self.handle_popup()
            
            # 等待页面加载完成
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "main")))
            logger.info("仪表板页面加载完成")
            
            return True
            
        except Exception as e:
            logger.error(f"导航到仪表板失败: {e}")
            return False
    
    def extract_subscription_links(self):
        """提取订阅链接"""
        subscription_links = {}

        try:
            # 等待页面完全加载
            time.sleep(3)

            # 处理可能出现的弹窗
            self.handle_popup()

            # 查找订阅链接按钮 - 使用实际观察到的选择器
            subscription_buttons = [
                {
                    'name': 'V2rayN订阅',
                    'selectors': [
                        "body > div:nth-of-type(1) > div > div > div > main > div:nth-of-type(1) > div > div:nth-of-type(4) > div:nth-of-type(1) > div > div:nth-of-type(4) > div > div:nth-of-type(3) > div > div:nth-of-type(3) > div:nth-of-type(3) > button",
                        "//button[text()='复制订阅']",
                        "//button[contains(text(), '复制订阅')]"
                    ]
                },
                {
                    'name': '通用订阅链接',
                    'selectors': [
                        "body > div:nth-of-type(1) > div > div > div > main > div:nth-of-type(1) > div > div:nth-of-type(4) > div:nth-of-type(1) > div > div:nth-of-type(5) > div > div:nth-of-type(2) > button",
                        "//button[text()='复制通用订阅链接']",
                        "//button[contains(text(), '复制通用订阅链接')]"
                    ]
                }
            ]

            for button_info in subscription_buttons:
                button_found = False

                for selector in button_info['selectors']:
                    try:
                        if selector.startswith('//'):
                            # XPath选择器
                            button = WebDriverWait(self.driver, 5).until(
                                EC.element_to_be_clickable((By.XPATH, selector))
                            )
                        else:
                            # CSS选择器
                            button = WebDriverWait(self.driver, 5).until(
                                EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                            )

                        if button and button.is_displayed():
                            logger.info(f"找到 {button_info['name']} 按钮")

                            # 滚动到按钮位置
                            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", button)
                            time.sleep(2)

                            # 清空剪贴板
                            pyperclip.copy("")

                            # 尝试点击按钮
                            try:
                                button.click()
                            except Exception:
                                # 如果普通点击失败，使用JavaScript点击
                                self.driver.execute_script("arguments[0].click();", button)

                            logger.info(f"已点击 {button_info['name']} 按钮")

                            # 等待剪贴板内容更新
                            time.sleep(3)

                            # 获取剪贴板内容
                            clipboard_content = pyperclip.paste()
                            if clipboard_content and clipboard_content.strip() and clipboard_content != "":
                                subscription_links[button_info['name']] = clipboard_content.strip()
                                logger.info(f"成功获取 {button_info['name']}: {clipboard_content[:50]}...")
                                button_found = True
                                break
                            else:
                                logger.warning(f"剪贴板内容为空，重试...")
                                time.sleep(2)
                                clipboard_content = pyperclip.paste()
                                if clipboard_content and clipboard_content.strip():
                                    subscription_links[button_info['name']] = clipboard_content.strip()
                                    logger.info(f"重试成功获取 {button_info['name']}: {clipboard_content[:50]}...")
                                    button_found = True
                                    break

                    except TimeoutException:
                        logger.debug(f"选择器超时: {selector}")
                        continue
                    except Exception as e:
                        logger.warning(f"尝试选择器 {selector} 时出错: {e}")
                        continue

                if not button_found:
                    logger.error(f"未能找到或点击 {button_info['name']} 按钮")

            return subscription_links

        except Exception as e:
            logger.error(f"提取订阅链接时出错: {e}")
            return {}
    
    def run(self, username, password):
        """运行完整的提取流程"""
        try:
            logger.info("开始Pokemon订阅链接提取流程")
            
            # 登录
            if not self.login(username, password):
                return None
            
            # 导航到仪表板
            if not self.navigate_to_dashboard():
                return None
            
            # 提取订阅链接
            subscription_links = self.extract_subscription_links()
            
            if subscription_links:
                logger.info("订阅链接提取完成")
                for name, link in subscription_links.items():
                    logger.info(f"{name}: {link}")
            else:
                logger.warning("未能提取到任何订阅链接")
            
            return subscription_links
            
        except Exception as e:
            logger.error(f"运行过程中出错: {e}")
            return None
        finally:
            self.close()
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    """主函数"""
    # 配置信息
    USERNAME = "your_username"  # 请替换为实际用户名
    PASSWORD = "your_password"  # 请替换为实际密码
    
    # 创建提取器实例
    extractor = PokemonSubscriptionExtractor(headless=False)
    
    try:
        # 运行提取流程
        result = extractor.run(USERNAME, PASSWORD)
        
        if result:
            print("\n=== 提取结果 ===")
            for name, link in result.items():
                print(f"{name}: {link}")
        else:
            print("提取失败，请检查日志")
            
    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")

if __name__ == "__main__":
    main()
