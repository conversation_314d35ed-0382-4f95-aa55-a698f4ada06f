# 📁 Email项目导航索引

## 🎯 **项目概览**

本目录包含邮箱管理和VPN注册相关的项目，专注于个人使用的自动化工具开发。

## 📂 **项目结构**

```
D:\Dev\Email\
├── 📁 OutlookManager/          # Outlook邮箱管理系统
├── 📁 VPNRegister/            # VPN批量注册系统（框架版本）
└── 📄 PROJECT_INDEX.md       # 项目导航索引（本文件）
```

## 🚀 **项目详情**

### 1. OutlookManager - Outlook邮箱管理系统

**状态**: ✅ 完全可用  
**路径**: `D:\Dev\Email\OutlookManager\`  
**功能**: 
- Outlook邮箱账户管理
- 邮件监控和获取
- 验证码自动提取
- Web API接口

**快速启动**:
```bash
cd D:\Dev\Email\OutlookManager
python main.py
# 访问: http://localhost:8010
```

**核心文件**:
- `main.py` - 主程序入口
- `accounts.json` - 邮箱账户数据
- `outlook_manager.py` - 核心管理器
- `email_monitor.py` - 邮件监控服务

### 2. VPNRegister - VPN批量注册系统

**状态**: ⚠️ 框架版本（需要实现浏览器自动化）  
**路径**: `D:\Dev\Email\VPNRegister\`  
**功能**: 
- VPN账户批量注册框架
- OutlookManager集成
- 数据库管理
- API接口设计

**快速启动**:
```bash
cd D:\Dev\Email\VPNRegister
python main.py
# 访问: http://localhost:8020
```

**核心文件**:
- `main.py` - 简化的API服务
- `config.py` - 配置管理
- `models/` - 数据模型
- `services/` - 业务服务（需要实现）
- `utils/` - 工具模块

## 🔧 **开发工具和依赖**

### 通用依赖
- Python 3.8+
- FastAPI
- aiohttp
- aiosqlite

### OutlookManager专用
- msal (Microsoft认证)
- requests

### VPNRegister专用
- uvicorn
- pydantic

## 📋 **使用指南**

### 1. 环境准备
```bash
# 安装Python依赖
pip install fastapi uvicorn aiohttp aiosqlite msal requests pydantic
```

### 2. OutlookManager使用
1. 配置邮箱账户到 `accounts.json`
2. 启动服务: `python main.py`
3. 通过API管理邮箱和获取邮件

### 3. VPNRegister使用
1. 确保OutlookManager服务运行
2. 启动框架服务: `python main.py`
3. 查看API文档了解接口设计
4. **注意**: 需要实现真实的浏览器自动化才能工作

## ⚠️ **重要说明**

### 项目状态
- **OutlookManager**: 生产可用，功能完整
- **VPNRegister**: 仅为架构演示，核心功能未实现

### 技术限制
- VPNRegister需要Selenium/Playwright实现浏览器自动化
- 需要反检测技术绕过网站保护
- 验证码识别功能需要OCR或第三方服务

### 使用建议
- OutlookManager可以直接用于邮箱管理
- VPNRegister仅作为学习参考，不建议直接使用
- 实际VPN需求建议购买现成服务

## 🔗 **相关链接**

- [OutlookManager API文档](http://localhost:8010/docs)
- [VPNRegister API文档](http://localhost:8020/docs)
- [Microsoft Graph API文档](https://docs.microsoft.com/en-us/graph/)

## 📝 **更新日志**

### 2025-01-27
- ✅ 完成代码清理和文档整理
- ✅ 创建项目导航索引
- ✅ 简化VPNRegister为框架版本
- ✅ 删除无用的模拟代码

### 历史记录
- OutlookManager: 功能完整，可正常使用
- VPNRegister: 架构设计完成，核心功能待实现

---

**📞 快速访问**:
- OutlookManager: http://localhost:8010
- VPNRegister: http://localhost:8020
- 项目目录: D:\Dev\Email\