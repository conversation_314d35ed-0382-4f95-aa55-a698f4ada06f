#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的验证码获取逻辑
确保获取最新邮件、使用正确邮箱、从正确字段提取验证码
"""

import requests
import re
import logging
import time
from datetime import datetime
from typing import Optional, List, Dict

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedVerificationCodeExtractor:
    def __init__(self):
        self.outlook_api_base = "http://localhost:8010"
        self.headers = {"Authorization": "Bearer admin123"}
    
    def get_available_accounts(self) -> List[Dict]:
        """获取可用的邮箱账户"""
        try:
            response = requests.get(f"{self.outlook_api_base}/accounts", headers=self.headers)
            if response.status_code == 200:
                accounts = response.json()
                logger.info(f"获取到 {len(accounts)} 个邮箱账户")
                return accounts
            else:
                logger.error(f"获取账户列表失败: {response.status_code}")
                return []
        except Exception as e:
            logger.error(f"获取账户列表异常: {e}")
            return []
    
    def get_latest_pokemon_email(self, email: str) -> Optional[Dict]:
        """获取最新的宝可梦验证码邮件"""
        try:
            # 方法1: 尝试使用latest API
            logger.info(f"尝试获取 {email} 的最新邮件...")
            response = requests.get(
                f"{self.outlook_api_base}/emails/{email}/latest",
                params={"include_content": True, "force_refresh": True},
                headers=self.headers
            )
            
            if response.status_code == 200:
                latest_email = response.json()
                subject = latest_email.get('subject', '')
                
                # 检查是否是宝可梦验证码邮件
                if any(keyword in subject.lower() for keyword in ['宝可梦', 'pokemon', '验证码']):
                    logger.info(f"最新邮件是宝可梦验证码: {subject}")
                    return latest_email
                else:
                    logger.info(f"最新邮件不是验证码邮件: {subject}")
            
            # 方法2: 搜索垃圾邮件文件夹中的验证码邮件
            logger.info("搜索垃圾邮件文件夹中的验证码邮件...")
            return self.search_pokemon_emails_in_folder(email, "junk")
            
        except Exception as e:
            logger.error(f"获取最新邮件异常: {e}")
            return None
    
    def search_pokemon_emails_in_folder(self, email: str, folder: str) -> Optional[Dict]:
        """在指定文件夹中搜索宝可梦验证码邮件"""
        try:
            response = requests.get(
                f"{self.outlook_api_base}/emails/{email}",
                params={"folder": folder, "page": 1, "page_size": 20, "force_refresh": True},
                headers=self.headers
            )
            
            if response.status_code != 200:
                logger.error(f"获取{folder}文件夹邮件失败: {response.status_code}")
                return None
            
            emails_data = response.json()
            emails = emails_data.get('emails', [])
            logger.info(f"{folder}文件夹中有 {len(emails)} 封邮件")
            
            # 查找宝可梦验证码邮件（按时间排序，最新的在前）
            pokemon_emails = []
            for email_item in emails:
                subject = email_item.get('subject', '')
                if any(keyword in subject.lower() for keyword in ['宝可梦', 'pokemon', '验证码']):
                    pokemon_emails.append(email_item)
                    logger.info(f"找到验证码邮件: {subject} ({email_item.get('date', 'N/A')})")
            
            if not pokemon_emails:
                logger.warning(f"{folder}文件夹中未找到宝可梦验证码邮件")
                return None
            
            # 获取最新的验证码邮件详情
            latest_pokemon = pokemon_emails[0]  # 假设API返回的邮件已按时间排序
            message_id = latest_pokemon.get('message_id')
            
            if not message_id:
                logger.error("邮件缺少message_id")
                return None
            
            # 获取邮件详细内容
            detail_response = requests.get(
                f"{self.outlook_api_base}/emails/{email}/{message_id}",
                headers=self.headers
            )
            
            if detail_response.status_code == 200:
                email_detail = detail_response.json()
                logger.info(f"成功获取邮件详情: {email_detail.get('subject', 'N/A')}")
                return email_detail
            else:
                logger.error(f"获取邮件详情失败: {detail_response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"搜索{folder}文件夹邮件异常: {e}")
            return None
    
    def extract_verification_code_from_email(self, email_detail: Dict) -> Optional[str]:
        """从邮件详情中提取验证码"""
        try:
            subject = email_detail.get('subject', '')
            body_plain = email_detail.get('body_plain', '') or ''
            body_html = email_detail.get('body_html', '') or ''
            
            logger.info(f"邮件主题: {subject}")
            logger.info(f"纯文本内容长度: {len(body_plain)}")
            logger.info(f"HTML内容长度: {len(body_html)}")
            
            # 合并所有文本内容
            content = f"{subject} {body_plain} {body_html}"
            
            # 显示内容预览（用于调试）
            preview = content.replace('\n', ' ').replace('\r', ' ')[:500]
            logger.info(f"内容预览: {preview}...")
            
            return self.extract_verification_code(content)
            
        except Exception as e:
            logger.error(f"从邮件提取验证码异常: {e}")
            return None
    
    def extract_verification_code(self, content: str) -> Optional[str]:
        """使用多种模式提取验证码"""
        
        # 模式1: 验证码: 123456 或 验证码：123456
        pattern1 = re.findall(r'验证码[：:]\s*(\d{4,6})', content, re.IGNORECASE)
        if pattern1:
            logger.info(f"✅ 模式1匹配: {pattern1[0]}")
            return pattern1[0]
        
        # 模式2: verification code: 123456
        pattern2 = re.findall(r'verification\s+code[：:]\s*(\d{4,6})', content, re.IGNORECASE)
        if pattern2:
            logger.info(f"✅ 模式2匹配: {pattern2[0]}")
            return pattern2[0]
        
        # 模式3: 您的验证码是 123456
        pattern3 = re.findall(r'您的验证码是\s*(\d{4,6})', content, re.IGNORECASE)
        if pattern3:
            logger.info(f"✅ 模式3匹配: {pattern3[0]}")
            return pattern3[0]
        
        # 模式4: 宝可梦特定格式 - 6位数字独立出现（最常见）
        pattern4 = re.findall(r'\b(\d{6})\b', content)
        if pattern4:
            # 过滤掉明显不是验证码的数字
            filtered_codes = []
            for code in pattern4:
                # 排除年份、端口号、常见数字等
                if not code.startswith(('19', '20', '80', '443', '8080', '000', '111', '222', '333', '444', '555', '666', '777', '888', '999')):
                    filtered_codes.append(code)
            
            if filtered_codes:
                logger.info(f"✅ 模式4匹配: {filtered_codes[0]} (从 {pattern4} 中筛选)")
                return filtered_codes[0]
        
        # 模式5: 通用4-6位数字（作为备选）
        pattern5 = re.findall(r'\b\d{4,6}\b', content)
        if pattern5:
            # 过滤掉明显不是验证码的数字
            filtered_codes = []
            for code in pattern5:
                if not code.startswith(('19', '20', '80', '443', '8080', '000')):
                    filtered_codes.append(code)
            
            if filtered_codes:
                logger.info(f"✅ 模式5匹配: {filtered_codes[0]} (从 {pattern5} 中筛选)")
                return filtered_codes[0]
        
        logger.warning("❌ 所有模式都未匹配到验证码")
        return None
    
    def get_verification_code_for_registration(self, target_email: Optional[str] = None, max_wait: int = 60) -> Optional[str]:
        """为注册获取验证码的主要方法"""
        logger.info("🚀 开始获取验证码...")
        
        # 1. 获取可用账户
        accounts = self.get_available_accounts()
        if not accounts:
            logger.error("❌ 没有可用的邮箱账户")
            return None
        
        # 2. 选择目标邮箱
        if target_email:
            # 使用指定的邮箱
            target_accounts = [acc for acc in accounts if acc.get('email') == target_email]
            if not target_accounts:
                logger.error(f"❌ 指定的邮箱 {target_email} 不在可用账户中")
                return None
            test_accounts = target_accounts
        else:
            # 使用所有可用账户
            test_accounts = accounts
        
        logger.info(f"📧 将测试 {len(test_accounts)} 个邮箱账户")
        
        # 3. 循环等待验证码
        start_time = time.time()
        while time.time() - start_time < max_wait:
            for account in test_accounts:
                email = account.get('email')
                if not email:
                    continue
                
                logger.info(f"🔍 检查邮箱: {email}")
                
                # 获取最新的宝可梦验证码邮件
                latest_email = self.get_latest_pokemon_email(email)
                if latest_email:
                    # 提取验证码
                    verification_code = self.extract_verification_code_from_email(latest_email)
                    if verification_code:
                        logger.info(f"🎉 成功提取验证码: {verification_code} (来自 {email})")
                        return verification_code
            
            # 等待一段时间后重试
            logger.info("⏳ 未找到验证码，3秒后重试...")
            time.sleep(3)
        
        logger.error("❌ 获取验证码超时")
        return None

def test_improved_verification():
    """测试改进的验证码获取"""
    extractor = ImprovedVerificationCodeExtractor()
    
    # 测试指定邮箱
    target_email = "<EMAIL>"
    
    logger.info(f"🧪 测试改进的验证码获取逻辑")
    logger.info(f"📧 目标邮箱: {target_email}")
    
    verification_code = extractor.get_verification_code_for_registration(
        target_email=target_email, 
        max_wait=30
    )
    
    if verification_code:
        logger.info(f"✅ 测试成功！验证码: {verification_code}")
        logger.info(f"📏 验证码长度: {len(verification_code)}")
        logger.info(f"🔢 验证码格式: {'6位数字' if len(verification_code) == 6 and verification_code.isdigit() else '其他格式'}")
    else:
        logger.error("❌ 测试失败，未能获取验证码")

if __name__ == "__main__":
    test_improved_verification()