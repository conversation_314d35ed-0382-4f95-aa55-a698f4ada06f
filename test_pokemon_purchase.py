#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pokemon 0元购买功能测试脚本
用于测试完整的购买流程和各个组件功能
"""

import time
import sys
from pokemon_subscription_extractor import PokemonSubscriptionExtractor
import logging

# 配置测试日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_pokemon_purchase.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class PokemonPurchaseTest:
    def __init__(self):
        self.extractor = None
        self.test_results = {}
    
    def setup_test(self, headless=False):
        """设置测试环境"""
        try:
            self.extractor = PokemonSubscriptionExtractor(headless=headless)
            logger.info("✅ 测试环境设置成功")
            return True
        except Exception as e:
            logger.error(f"❌ 测试环境设置失败: {e}")
            return False
    
    def test_login_functionality(self, username, password):
        """测试登录功能"""
        logger.info("🧪 测试登录功能")
        try:
            result = self.extractor.login(username, password)
            self.test_results['login'] = result
            
            if result:
                logger.info("✅ 登录测试通过")
            else:
                logger.error("❌ 登录测试失败")
            
            return result
        except Exception as e:
            logger.error(f"❌ 登录测试出错: {e}")
            self.test_results['login'] = False
            return False
    
    def test_navigation(self):
        """测试页面导航功能"""
        logger.info("🧪 测试页面导航")
        try:
            result = self.extractor.navigate_to_plan_page()
            self.test_results['navigation'] = result
            
            if result:
                logger.info("✅ 导航测试通过")
            else:
                logger.error("❌ 导航测试失败")
            
            return result
        except Exception as e:
            logger.error(f"❌ 导航测试出错: {e}")
            self.test_results['navigation'] = False
            return False
    
    def test_plan_selection(self):
        """测试套餐选择功能"""
        logger.info("🧪 测试套餐选择")
        try:
            result = self.extractor.select_starter_plan()
            self.test_results['plan_selection'] = result
            
            if result:
                logger.info("✅ 套餐选择测试通过")
            else:
                logger.error("❌ 套餐选择测试失败")
            
            return result
        except Exception as e:
            logger.error(f"❌ 套餐选择测试出错: {e}")
            self.test_results['plan_selection'] = False
            return False
    
    def test_discount_code(self, code="可达鸭"):
        """测试优惠码应用功能"""
        logger.info(f"🧪 测试优惠码应用: {code}")
        try:
            result = self.extractor.apply_discount_code(code)
            self.test_results['discount_code'] = result
            
            if result:
                logger.info("✅ 优惠码测试通过")
            else:
                logger.error("❌ 优惠码测试失败")
            
            return result
        except Exception as e:
            logger.error(f"❌ 优惠码测试出错: {e}")
            self.test_results['discount_code'] = False
            return False
    
    def test_price_verification(self):
        """测试价格验证功能"""
        logger.info("🧪 测试价格验证")
        try:
            result = self.extractor.verify_price_change(6.9, 0.0)
            self.test_results['price_verification'] = result
            
            if result:
                logger.info("✅ 价格验证测试通过")
            else:
                logger.error("❌ 价格验证测试失败")
            
            return result
        except Exception as e:
            logger.error(f"❌ 价格验证测试出错: {e}")
            self.test_results['price_verification'] = False
            return False
    
    def test_complete_flow(self, username, password, discount_code="可达鸭", dry_run=True):
        """测试完整购买流程 - 基于Chrome MCP验证的完整流程"""
        logger.info(f"🧪 测试完整购买流程 (干运行: {dry_run})")

        if dry_run:
            logger.info("⚠️ 这是干运行模式，不会实际执行最终购买")
        else:
            logger.info("🚨 这是实际购买模式，将执行真实的0元购买！")

        try:
            # 基于Chrome MCP验证的完整步骤
            steps = [
                ("🔐 用户登录", lambda: self.test_login_functionality(username, password)),
                ("🏠 导航到套餐页面", self.test_navigation),
                ("🎯 选择入门精灵球套餐", self.test_plan_selection),
                ("🎫 应用优惠码", lambda: self.test_discount_code(discount_code)),
                ("💰 验证价格变为0元", self.test_price_verification)
            ]

            all_passed = True
            for step_name, step_func in steps:
                logger.info(f"📋 执行步骤: {step_name}")
                result = step_func()

                if not result:
                    logger.error(f"❌ 步骤 '{step_name}' 失败")
                    all_passed = False
                    break
                else:
                    logger.info(f"✅ 步骤 '{step_name}' 成功")

                time.sleep(2)  # 步骤间延迟

            if all_passed:
                if not dry_run:
                    # 实际购买模式：执行最终购买确认
                    logger.info("🛒 执行最终购买确认")
                    purchase_result = self.extractor.confirm_purchase()
                    self.test_results['purchase_confirmation'] = purchase_result

                    if purchase_result:
                        logger.info("🎉 购买确认成功！0元购买完成！")
                    else:
                        logger.error("❌ 购买确认失败")
                        all_passed = False
                else:
                    # 干运行模式：模拟购买确认
                    logger.info("🎭 干运行模式：模拟购买确认步骤")
                    logger.info("📝 在实际模式下，此时会:")
                    logger.info("   1. 选择每月付款选项")
                    logger.info("   2. 点击订购按钮")
                    logger.info("   3. 跳转到订单页面")
                    logger.info("   4. 确认订单状态为'已完成'")
                    logger.info("   5. 验证支付状态为'已支付'")
                    logger.info("   6. 确认实付金额为¥0.00")
                    self.test_results['purchase_confirmation'] = True

            self.test_results['complete_flow'] = all_passed

            if all_passed:
                logger.info("🎉 完整流程测试成功！")
                if dry_run:
                    logger.info("💡 提示：运行实际模式以执行真实的0元购买")

            return all_passed

        except Exception as e:
            logger.error(f"❌ 完整流程测试出错: {e}")
            self.test_results['complete_flow'] = False
            return False
    
    def print_test_summary(self):
        """打印测试结果摘要"""
        print("\n" + "="*50)
        print("🧪 Pokemon购买功能测试结果摘要")
        print("="*50)
        
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name.ljust(20)}: {status}")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        
        print(f"\n总测试数: {total_tests}")
        print(f"通过数量: {passed_tests}")
        print(f"失败数量: {total_tests - passed_tests}")
        print(f"通过率: {(passed_tests/total_tests*100):.1f}%")
        print("="*50)
    
    def cleanup(self):
        """清理测试环境"""
        if self.extractor:
            self.extractor.close()
            logger.info("🧹 测试环境已清理")

def main():
    """主测试函数"""
    print("Pokemon 0元购买功能测试")
    print("=" * 40)
    
    # 获取测试参数
    username = input("请输入测试用户名: ").strip()
    password = input("请输入测试密码: ").strip()
    discount_code = input("请输入优惠码 (默认: 可达鸭): ").strip() or "可达鸭"
    
    if not username or not password:
        print("❌ 用户名和密码不能为空")
        return
    
    # 选择测试模式
    print("\n测试模式选择:")
    print("1. 干运行模式 (推荐) - 测试所有步骤但不实际购买")
    print("2. 完整模式 - 包含实际购买操作")
    print("3. 单步测试 - 测试单个功能")
    
    mode = input("请选择模式 (1-3): ").strip()
    
    # 创建测试实例
    test = PokemonPurchaseTest()
    
    try:
        # 设置测试环境
        if not test.setup_test(headless=False):
            print("❌ 测试环境设置失败")
            return
        
        if mode == "1":
            # 干运行模式
            print("\n🧪 开始干运行测试...")
            test.test_complete_flow(username, password, discount_code, dry_run=True)
            
        elif mode == "2":
            # 完整模式
            print("\n⚠️ 警告: 这将执行实际的购买操作!")
            confirm = input("确认继续? (yes/no): ").strip().lower()
            
            if confirm == "yes":
                print("\n🛒 开始完整购买测试...")
                test.test_complete_flow(username, password, discount_code, dry_run=False)
            else:
                print("❌ 用户取消操作")
                
        elif mode == "3":
            # 单步测试
            print("\n🔧 单步测试模式")
            print("请手动执行各个测试步骤...")
            
            # 这里可以添加交互式单步测试
            test.test_login_functionality(username, password)
            
        else:
            print("❌ 无效的模式选择")
            return
        
        # 打印测试摘要
        test.print_test_summary()
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        logger.error(f"测试异常: {e}")
    finally:
        test.cleanup()

if __name__ == "__main__":
    main()
