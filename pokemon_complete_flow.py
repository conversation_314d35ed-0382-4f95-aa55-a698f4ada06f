#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pokemon完整流程脚本
注册 → 购买 → 提取订阅链接的完整自动化流程
"""

import asyncio
import sys
import os
import time
import logging
from datetime import datetime

# 导入Pokemon提取器
from pokemon_subscription_extractor import PokemonSubscriptionExtractor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'pokemon_complete_flow_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PokemonCompleteFlow:
    def __init__(self, email=None):
        """初始化完整流程"""
        self.email = email
        self.password = None
        self.pokemon_extractor = None
        
    async def execute_complete_flow(self):
        """执行完整的注册→购买→提取流程"""
        try:
            logger.info("开始Pokemon完整自动化流程")
            logger.info("=" * 60)

            # 阶段1: 注册账户
            logger.info("阶段1: 注册Pokemon账户")
            if not await self.register_account():
                logger.error("注册失败，终止流程")
                return False

            # 等待一段时间确保注册完成
            logger.info("等待注册完成...")
            time.sleep(5)

            # 阶段2: 执行0元购买
            logger.info("阶段2: 执行0元购买")
            if not await self.execute_purchase():
                logger.error("购买失败，终止流程")
                return False

            # 阶段3: 提取订阅链接
            logger.info("阶段3: 提取订阅链接")
            subscription_links = await self.extract_subscription_links()
            
            if subscription_links:
                logger.info("🎉 完整流程执行成功！")
                self.print_results(subscription_links)
                return True
            else:
                logger.warning("⚠️ 订阅链接提取失败，但购买可能已成功")
                return True
                
        except Exception as e:
            logger.error(f"❌ 完整流程执行出错: {e}")
            return False
        finally:
            # 清理资源
            if self.pokemon_extractor:
                self.pokemon_extractor.close()
    
    async def register_account(self):
        """注册Pokemon账户"""
        try:
            if self.email:
                logger.info(f"📧 使用指定邮箱: {self.email}")
                # 对于指定邮箱，使用简化的注册流程
                self.password = "Pokemon123456"  # 使用固定密码

                # 直接调用注册流程
                result = await self.execute_simple_registration()

                if result:
                    logger.info(f"✅ 注册成功: {self.email}")
                    return True
                else:
                    logger.error("❌ 注册失败")
                    return False
            else:
                logger.error("❌ 未提供邮箱，无法进行注册")
                return False

        except Exception as e:
            logger.error(f"❌ 注册过程出错: {e}")
            return False

    async def execute_simple_registration(self):
        """执行简化的注册流程"""
        try:
            from selenium import webdriver
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.chrome.options import Options
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service

            logger.info("🌐 启动浏览器进行注册")

            # 配置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.binary_location = r"C:\Program Files\Google\Chrome\Application\chrome.exe"

            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            try:
                # 访问注册页面
                register_url = "https://web2.52pokemon.cc/register"
                logger.info(f"访问注册页面: {register_url}")
                driver.get(register_url)

                wait = WebDriverWait(driver, 10)
                time.sleep(3)

                # 填写邮箱前缀
                email_prefix = self.email.split('@')[0]
                logger.info(f"填写邮箱前缀: {email_prefix}")

                email_input = wait.until(EC.presence_of_element_located((By.ID, "input-12")))
                email_input.clear()
                email_input.send_keys(email_prefix)

                # 选择@outlook.com后缀
                logger.info("选择邮箱后缀: @outlook.com")
                suffix_dropdown = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, ".v-select")))
                suffix_dropdown.click()
                time.sleep(1)

                outlook_option = wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), '@outlook.com')]")))
                outlook_option.click()

                # 填写密码
                logger.info("填写密码")
                password_input = wait.until(EC.presence_of_element_located((By.ID, "input-15")))
                password_input.clear()
                password_input.send_keys(self.password)

                # 确认密码
                confirm_password_input = wait.until(EC.presence_of_element_located((By.ID, "input-18")))
                confirm_password_input.clear()
                confirm_password_input.send_keys(self.password)

                # 点击注册按钮
                logger.info("点击注册按钮")
                register_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '注册')]")))
                register_button.click()

                # 等待注册完成
                time.sleep(5)

                # 检查是否注册成功（通过URL变化判断）
                current_url = driver.current_url
                if 'register' not in current_url or 'login' in current_url or 'dashboard' in current_url:
                    logger.info("✅ 注册成功，页面已跳转")
                    return True
                else:
                    logger.warning("⚠️ 注册状态不明确，但继续流程")
                    return True

            finally:
                driver.quit()

        except Exception as e:
            logger.error(f"❌ 简化注册流程出错: {e}")
            return False
    
    async def execute_purchase(self):
        """执行0元购买流程"""
        try:
            # 创建Pokemon提取器实例
            self.pokemon_extractor = PokemonSubscriptionExtractor(headless=False)
            
            # 执行完整购买流程
            success = self.pokemon_extractor.complete_purchase_flow(
                username=self.email,
                password=self.password,
                discount_code="可达鸭"
            )
            
            if success:
                logger.info("✅ 0元购买完成")
                return True
            else:
                logger.error("❌ 购买失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 购买过程出错: {e}")
            return False
    
    async def extract_subscription_links(self):
        """提取订阅链接"""
        try:
            if not self.pokemon_extractor:
                logger.error("❌ Pokemon提取器未初始化")
                return None
            
            # 导航到仪表板
            if self.pokemon_extractor.navigate_to_dashboard():
                # 提取订阅链接
                links = self.pokemon_extractor.extract_subscription_links()
                
                if links:
                    logger.info(f"✅ 成功提取 {len(links)} 个订阅链接")
                    return links
                else:
                    logger.warning("⚠️ 未找到订阅链接")
                    return None
            else:
                logger.error("❌ 无法导航到仪表板")
                return None
                
        except Exception as e:
            logger.error(f"❌ 提取订阅链接出错: {e}")
            return None
    
    def print_results(self, subscription_links):
        """打印最终结果"""
        print("\n" + "=" * 60)
        print("🎉 Pokemon完整流程执行成功！")
        print("=" * 60)
        print(f"📧 注册邮箱: {self.email}")
        print(f"🔑 登录密码: {self.password}")
        print(f"💰 购买状态: ✅ 0元购买成功")
        print(f"🔗 订阅链接数量: {len(subscription_links) if subscription_links else 0}")
        
        if subscription_links:
            print("\n📋 订阅链接详情:")
            for name, link in subscription_links.items():
                print(f"  {name}: {link}")
        
        print("\n💡 提示:")
        print("  - 请保存好登录信息和订阅链接")
        print("  - 订阅链接可用于VPN客户端配置")
        print("  - 账户已激活，可随时登录使用")
        print("=" * 60)

async def main():
    """主函数"""
    print("Pokemon完整自动化流程")
    print("=" * 40)
    print("功能: 注册 → 购买 → 提取订阅链接")
    print()

    # 使用指定的邮箱
    email = "<EMAIL>"
    print(f"使用指定邮箱: {email}")

    print("\n开始执行完整流程...")

    # 创建流程实例
    flow = PokemonCompleteFlow(email=email)

    try:
        # 执行完整流程
        success = await flow.execute_complete_flow()

        if success:
            print("\n所有流程执行完成！")
        else:
            print("\n流程执行失败，请检查日志")

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n程序出错: {e}")
        logger.error(f"主程序异常: {e}")

# 同步版本的主函数，用于直接调用
def run_complete_flow():
    """同步版本的完整流程执行函数"""
    asyncio.run(main())

if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
