#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试验证码填写和创建账户按钮点击
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_button_click.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

async def test_button_click():
    """测试按钮点击功能"""
    logger.info("开始测试验证码填写和按钮点击")
    
    try:
        from patchright.async_api import async_playwright
        
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch_persistent_context(
                user_data_dir="./patchright_profile",
                headless=False,
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--no-sandbox',
                    '--disable-web-security'
                ]
            )
            
            try:
                page = browser.pages[0] if browser.pages else await browser.new_page()
                
                # 访问注册页面
                logger.info("访问注册页面...")
                await page.goto("https://web2.52pokemon.cc/register", wait_until='domcontentloaded')
                
                # 等待页面加载
                await page.wait_for_selector("input[placeholder*='邮箱']", timeout=10000)
                logger.info("页面已加载")
                
                # 填写邮箱
                email_input = await page.wait_for_selector("input[type='text']:first-of-type", timeout=5000)
                await email_input.fill("testuser123")
                logger.info("邮箱前缀已填写")
                
                # 等待一下让用户手动操作其他字段
                logger.info("请手动填写密码等其他字段，然后按回车继续...")
                await page.wait_for_timeout(30000)  # 等待30秒
                
                # 测试验证码输入框
                logger.info("测试验证码输入框...")
                code_selectors = [
                    "#input-26",
                    "input[placeholder*='验证码']",
                    "input[placeholder*='请输入验证码']",
                    "input[type='text']:last-of-type"
                ]
                
                code_input = None
                for selector in code_selectors:
                    try:
                        code_input = await page.wait_for_selector(selector, timeout=3000)
                        if code_input:
                            is_disabled = await code_input.is_disabled()
                            is_visible = await code_input.is_visible()
                            
                            if not is_disabled and is_visible:
                                logger.info(f"找到验证码输入框: {selector}")
                                break
                            else:
                                code_input = None
                    except:
                        continue
                
                if code_input:
                    # 填写测试验证码
                    test_code = "123456"
                    logger.info(f"填写测试验证码: {test_code}")
                    
                    await code_input.scroll_into_view_if_needed()
                    await code_input.click()
                    await page.wait_for_timeout(300)
                    await code_input.fill("")
                    await code_input.fill(test_code)
                    await page.wait_for_timeout(500)
                    
                    # 验证输入
                    input_value = await code_input.input_value()
                    logger.info(f"验证码输入结果: {input_value}")
                else:
                    logger.error("未找到验证码输入框")
                
                # 测试创建账户按钮
                logger.info("测试创建账户按钮...")
                submit_selectors = [
                    "body > div:nth-of-type(1) > div > div > div:nth-of-type(2) > div:nth-of-type(2) > div > div > div > div > div > div:nth-of-type(2) > form > button",
                    "form > button",
                    "button:has-text('创建账号')",
                    "button[type='submit']"
                ]
                
                submit_button = None
                for selector in submit_selectors:
                    try:
                        submit_button = await page.wait_for_selector(selector, timeout=3000)
                        if submit_button:
                            is_disabled = await submit_button.is_disabled()
                            is_visible = await submit_button.is_visible()
                            text = await submit_button.text_content()
                            
                            logger.info(f"找到按钮: {selector}, 文本: '{text}', 可用: {not is_disabled}, 可见: {is_visible}")
                            
                            if not is_disabled and is_visible:
                                logger.info(f"找到可用的提交按钮: {selector}")
                                break
                            else:
                                submit_button = None
                    except Exception as e:
                        logger.debug(f"选择器 {selector} 失败: {e}")
                        continue
                
                if submit_button:
                    logger.info("准备点击创建账户按钮...")
                    await submit_button.scroll_into_view_if_needed()
                    await page.wait_for_timeout(500)
                    
                    # 点击按钮
                    try:
                        await submit_button.click()
                        logger.info("成功点击创建账户按钮")
                    except Exception as e:
                        logger.warning(f"直接点击失败，尝试JavaScript点击: {e}")
                        await page.evaluate("arguments[0].click()", submit_button)
                        logger.info("使用JavaScript成功点击创建账户按钮")
                    
                    # 等待页面响应
                    await page.wait_for_timeout(5000)
                    
                    # 检查页面变化
                    current_url = page.url
                    logger.info(f"点击后页面URL: {current_url}")
                    
                else:
                    logger.error("未找到提交按钮")
                    # 显示所有按钮
                    all_buttons = await page.query_selector_all("button")
                    logger.info(f"页面上共有 {len(all_buttons)} 个按钮")
                    for i, btn in enumerate(all_buttons):
                        text = await btn.text_content()
                        logger.info(f"按钮 {i+1}: '{text}'")
                
                # 保持浏览器打开以便观察
                logger.info("测试完成，等待10秒后关闭浏览器...")
                await page.wait_for_timeout(10000)
                
            finally:
                await browser.close()
                
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False
    
    return True

async def main():
    """主函数"""
    logger.info("验证码填写和按钮点击测试")
    logger.info("=" * 50)
    
    success = await test_button_click()
    
    logger.info("=" * 50)
    if success:
        logger.info("测试完成")
    else:
        logger.info("测试失败")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
