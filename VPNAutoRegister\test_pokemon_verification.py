#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试宝可梦验证码提取功能
基于实际邮件格式验证API对接
"""

import asyncio
import httpx
import logging
import re
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_pokemon_verification.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class PokemonVerificationTester:
    def __init__(self):
        self.outlook_api_base = "http://localhost:8010"  # 注意端口是8010
        self.headers = {"Authorization": "Bearer admin123"}
    
    async def test_pokemon_verification_extraction(self, email):
        """测试宝可梦验证码提取"""
        logger.info(f"🔍 测试宝可梦验证码提取: {email}")
        
        try:
            async with httpx.AsyncClient() as client:
                # 搜索垃圾邮件文件夹（验证码邮件通常在这里）
                logger.info("📁 搜索垃圾邮件文件夹...")
                response = await client.get(
                    f"{self.outlook_api_base}/emails/{email}",
                    params={"folder": "junk", "page": 1, "page_size": 10},
                    headers=self.headers
                )
                
                if response.status_code == 200:
                    emails_data = response.json()
                    logger.info(f"✅ 垃圾邮件获取成功，共 {len(emails_data.get('emails', []))} 封邮件")
                    
                    # 查找宝可梦验证码邮件
                    pokemon_emails = []
                    for email_item in emails_data.get('emails', []):
                        subject = email_item.get('subject', '')
                        message_id = email_item.get('message_id', '')
                        date = email_item.get('date', '')
                        
                        # 检查是否是宝可梦验证码邮件
                        if any(keyword in subject.lower() for keyword in ['宝可梦', 'pokemon', '验证码']):
                            pokemon_emails.append({
                                'subject': subject,
                                'message_id': message_id,
                                'date': date
                            })
                            logger.info(f"🎯 找到宝可梦验证码邮件: {subject} ({date})")
                    
                    if not pokemon_emails:
                        logger.warning("⚠️ 未找到宝可梦验证码邮件")
                        return None
                    
                    # 获取最新的验证码邮件详情
                    latest_email = pokemon_emails[0]  # 假设第一个是最新的
                    logger.info(f"📧 获取最新验证码邮件详情: {latest_email['subject']}")
                    
                    detail_response = await client.get(
                        f"{self.outlook_api_base}/emails/{email}/{latest_email['message_id']}",
                        headers=self.headers
                    )
                    
                    if detail_response.status_code == 200:
                        email_detail = detail_response.json()
                        subject = email_detail.get('subject', '')
                        body_plain = email_detail.get('body_plain', '')
                        body_html = email_detail.get('body_html', '')
                        
                        logger.info(f"📄 邮件主题: {subject}")
                        logger.info(f"📝 纯文本内容长度: {len(body_plain)}")
                        logger.info(f"🌐 HTML内容长度: {len(body_html)}")
                        
                        # 合并所有文本内容进行搜索
                        content = f"{subject} {body_plain} {body_html}"
                        logger.info(f"🔍 内容预览: {content[:300]}...")
                        
                        # 使用多种验证码提取模式
                        verification_code = self.extract_verification_code(content)
                        
                        if verification_code:
                            logger.info(f"✅ 成功提取验证码: {verification_code}")
                            logger.info(f"📏 验证码长度: {len(verification_code)}")
                            logger.info(f"🔢 验证码格式: {'6位数字' if len(verification_code) == 6 and verification_code.isdigit() else '其他格式'}")
                            return verification_code
                        else:
                            logger.error("❌ 验证码提取失败")
                            return None
                    else:
                        logger.error(f"❌ 获取邮件详情失败: {detail_response.status_code}")
                        return None
                else:
                    logger.error(f"❌ 获取垃圾邮件失败: {response.status_code} - {response.text}")
                    return None
                    
        except Exception as e:
            logger.error(f"❌ 测试过程中发生异常: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None
    
    def extract_verification_code(self, content):
        """提取验证码 - 使用多种模式"""
        
        # 模式1: 验证码: 123456 或 验证码：123456
        pattern1 = re.findall(r'验证码[：:]\s*(\d{4,6})', content, re.IGNORECASE)
        if pattern1:
            logger.info(f"🎯 模式1匹配: {pattern1[0]}")
            return pattern1[0]
        
        # 模式2: verification code: 123456
        pattern2 = re.findall(r'verification\s+code[：:]\s*(\d{4,6})', content, re.IGNORECASE)
        if pattern2:
            logger.info(f"🎯 模式2匹配: {pattern2[0]}")
            return pattern2[0]
        
        # 模式3: 您的验证码是 123456
        pattern3 = re.findall(r'您的验证码是\s*(\d{4,6})', content, re.IGNORECASE)
        if pattern3:
            logger.info(f"🎯 模式3匹配: {pattern3[0]}")
            return pattern3[0]
        
        # 模式4: 宝可梦特定格式 - 6位数字独立出现
        pattern4 = re.findall(r'\b(\d{6})\b', content)
        if pattern4:
            # 过滤掉明显不是验证码的数字
            filtered_codes = [code for code in pattern4 if not code.startswith(('19', '20', '80', '443', '8080', '000', '111'))]
            if filtered_codes:
                logger.info(f"🎯 模式4匹配: {filtered_codes[0]}")
                return filtered_codes[0]
        
        # 模式5: 通用4-6位数字（作为备选）
        pattern5 = re.findall(r'\b\d{4,6}\b', content)
        if pattern5:
            # 过滤掉明显不是验证码的数字
            filtered_codes = [code for code in pattern5 if not code.startswith(('19', '20', '80', '443', '8080'))]
            if filtered_codes:
                logger.info(f"🎯 模式5匹配: {filtered_codes[0]}")
                return filtered_codes[0]
        
        logger.warning("❌ 所有模式都未匹配到验证码")
        return None

async def main():
    """主测试函数"""
    logger.info("🚀 开始宝可梦验证码提取测试")
    logger.info(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = PokemonVerificationTester()
    
    # 使用实际的测试邮箱
    test_email = "<EMAIL>"
    
    logger.info(f"📧 测试邮箱: {test_email}")
    
    # 执行验证码提取测试
    verification_code = await tester.test_pokemon_verification_extraction(test_email)
    
    if verification_code:
        logger.info(f"🎉 测试成功！提取到验证码: {verification_code}")
        logger.info("✅ API对接工作正常，验证码提取逻辑正确")
    else:
        logger.error("❌ 测试失败，未能提取到验证码")
        logger.info("💡 可能的原因:")
        logger.info("   - OutlookManager服务未启动或端口错误")
        logger.info("   - 邮箱中没有新的宝可梦验证码邮件")
        logger.info("   - 验证码邮件格式发生变化")
        logger.info("   - API认证失败")
    
    logger.info("🏁 测试完成")

if __name__ == "__main__":
    asyncio.run(main())