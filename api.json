{"openapi": "3.1.0", "info": {"title": "Outlook邮件API服务", "description": "基于FastAPI和aioimaplib的异步邮件管理服务", "version": "1.0.0"}, "paths": {"/auth/config": {"get": {"summary": "Get Auth Config", "description": "获取认证配置信息（用于前端判断认证状态）", "operationId": "get_auth_config_auth_config_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/accounts": {"post": {"summary": "Register Account", "description": "注册或更新邮箱账户，支持单个或批量\n\nArgs:\n    credentials: 单个账户凭证或账户凭证列表\n\nReturns:\n    单个账户响应或账户响应列表", "operationId": "register_account_accounts_post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/AccountCredentials"}, {"type": "array", "items": {"$ref": "#/components/schemas/AccountCredentials"}}], "title": "Credentials"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/AccountResponse"}, {"type": "array", "items": {"$ref": "#/components/schemas/AccountResponse"}}], "title": "Response Register Account Accounts Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"summary": "Get Accounts", "description": "获取所有账户列表，可选择检查账户活性状态\n\nArgs:\n    check_status: 是否检查账户活性状态", "operationId": "get_accounts_accounts_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "check_status", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Check Status"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AccountStatus"}, "title": "Response Get Accounts Accounts Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"summary": "Delete Multiple Accounts", "description": "批量删除账户\n\nArgs:\n    request: 包含要删除的邮箱地址列表的请求\n\nReturns:\n    删除操作的结果统计", "operationId": "delete_multiple_accounts_accounts_delete", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountDeleteRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/accounts/with-credentials": {"get": {"summary": "Get Accounts With Credentials", "description": "获取所有账户列表，包含凭证信息\n\nArgs:\n    check_status: 是否检查账户活性状态", "operationId": "get_accounts_with_credentials_accounts_with_credentials_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "check_status", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Check Status"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AccountWithCredentials"}, "title": "Response Get Accounts With Credentials Accounts With Credentials Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/emails/{email_id}": {"get": {"summary": "Get Emails", "description": "获取邮件列表", "operationId": "get_emails_emails__email_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "email_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Email <PERSON>d"}}, {"name": "folder", "in": "query", "required": false, "schema": {"type": "string", "pattern": "^(inbox|junk|all)$", "default": "all", "title": "Folder"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "page_size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 500, "minimum": 1, "default": 100, "title": "<PERSON>"}}, {"name": "force_refresh", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Force Refresh"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/emails/{email_id}/dual-view": {"get": {"summary": "Get Dual View Emails", "description": "获取双栏视图邮件（收件箱和垃圾箱）", "operationId": "get_dual_view_emails_emails__email_id__dual_view_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "email_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Email <PERSON>d"}}, {"name": "inbox_page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Inbox Page"}}, {"name": "junk_page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Junk Page"}}, {"name": "page_size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 20, "title": "<PERSON>"}}, {"name": "force_refresh", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Force Refresh"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/emails/{email_id}/latest": {"get": {"summary": "Get Latest Email", "description": "获取账户的真正最新一封邮件（包括收件箱和垃圾邮件）\n\nArgs:\n    email_id: 邮箱地址\n    include_content: 是否包含邮件正文内容（用于展开预览）\n    force_refresh: 是否强制刷新缓存", "operationId": "get_latest_email_emails__email_id__latest_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "email_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Email <PERSON>d"}}, {"name": "include_content", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Include Content"}}, {"name": "force_refresh", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Force Refresh"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/emails/{email_id}/{message_id}": {"get": {"summary": "Get Email Detail", "description": "获取邮件详细内容", "operationId": "get_email_detail_emails__email_id___message_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "email_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Email <PERSON>d"}}, {"name": "message_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Message Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailDetailsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/": {"get": {"summary": "Root", "description": "根路径 - 返回前端页面", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api": {"get": {"summary": "Api Status", "description": "API状态检查", "operationId": "api_status_api_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/accounts/verify": {"post": {"summary": "Verify Accounts", "description": "批量验证账户凭证有效性（优化版本）\n\nArgs:\n    request: 包含多个账户凭证的请求\n\nReturns:\n    每个账户的验证结果列表", "operationId": "verify_accounts_accounts_verify_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountVerificationRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/AccountVerificationResult"}, "type": "array", "title": "Response Verify Accounts Accounts Verify Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/tasks/create": {"post": {"summary": "Create Task", "description": "创建异步任务", "operationId": "create_task_tasks_create_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/tasks/{task_id}": {"get": {"summary": "Get Task Status", "description": "获取任务状态", "operationId": "get_task_status_tasks__task_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/accounts/import": {"post": {"summary": "Import Verified Accounts", "description": "导入已验证的账户（不进行重复验证，直接保存）\n\nArgs:\n    credentials: 已验证的账户凭证列表\n\nReturns:\n    导入结果列表", "operationId": "import_verified_accounts_accounts_import_post", "requestBody": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/AccountCredentials"}, "type": "array", "title": "Credentials"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/AccountResponse"}, "type": "array", "title": "Response Import Verified Accounts Accounts Import Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}}, "components": {"schemas": {"AccountCredentials": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "refresh_token": {"type": "string", "title": "Refresh <PERSON>"}, "client_id": {"type": "string", "title": "Client Id"}}, "type": "object", "required": ["email", "refresh_token", "client_id"], "title": "AccountCredentials"}, "AccountDeleteRequest": {"properties": {"emails": {"items": {"type": "string", "format": "email"}, "type": "array", "title": "Emails"}}, "type": "object", "required": ["emails"], "title": "AccountDeleteRequest"}, "AccountResponse": {"properties": {"email_id": {"type": "string", "title": "Email <PERSON>d"}, "message": {"type": "string", "title": "Message"}}, "type": "object", "required": ["email_id", "message"], "title": "AccountResponse"}, "AccountStatus": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "status": {"type": "string", "title": "Status", "default": "unknown"}}, "type": "object", "required": ["email"], "title": "Account<PERSON><PERSON><PERSON>"}, "AccountVerificationRequest": {"properties": {"accounts": {"items": {"$ref": "#/components/schemas/AccountCredentials"}, "type": "array", "title": "Accounts"}}, "type": "object", "required": ["accounts"], "title": "AccountVerificationRequest"}, "AccountVerificationResult": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "status": {"type": "string", "title": "Status"}, "message": {"type": "string", "title": "Message", "default": ""}, "credentials": {"anyOf": [{"$ref": "#/components/schemas/AccountCredentials"}, {"type": "null"}]}}, "type": "object", "required": ["email", "status"], "title": "AccountVerificationResult"}, "AccountWithCredentials": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "status": {"type": "string", "title": "Status", "default": "unknown"}, "refresh_token": {"type": "string", "title": "Refresh <PERSON>"}, "client_id": {"type": "string", "title": "Client Id"}}, "type": "object", "required": ["email", "refresh_token", "client_id"], "title": "AccountWithCredentials"}, "EmailDetailsResponse": {"properties": {"message_id": {"type": "string", "title": "Message Id"}, "subject": {"type": "string", "title": "Subject"}, "from_email": {"type": "string", "title": "From Email"}, "to_email": {"type": "string", "title": "To Email"}, "date": {"type": "string", "title": "Date"}, "body_plain": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Body Plain"}, "body_html": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Body Html"}}, "type": "object", "required": ["message_id", "subject", "from_email", "to_email", "date"], "title": "EmailDetailsResponse"}, "EmailItem": {"properties": {"message_id": {"type": "string", "title": "Message Id"}, "folder": {"type": "string", "title": "Folder"}, "subject": {"type": "string", "title": "Subject"}, "from_email": {"type": "string", "title": "From Email"}, "date": {"type": "string", "title": "Date"}, "is_read": {"type": "boolean", "title": "<PERSON>", "default": false}, "has_attachments": {"type": "boolean", "title": "Has Attachments", "default": false}, "sender_initial": {"type": "string", "title": "Sender Initial", "default": "?"}}, "type": "object", "required": ["message_id", "folder", "subject", "from_email", "date"], "title": "EmailItem"}, "EmailListResponse": {"properties": {"email_id": {"type": "string", "title": "Email <PERSON>d"}, "folder_view": {"type": "string", "title": "Folder View"}, "page": {"type": "integer", "title": "Page"}, "page_size": {"type": "integer", "title": "<PERSON>"}, "total_emails": {"type": "integer", "title": "Total Emails"}, "emails": {"items": {"$ref": "#/components/schemas/EmailItem"}, "type": "array", "title": "Emails"}}, "type": "object", "required": ["email_id", "folder_view", "page", "page_size", "total_emails", "emails"], "title": "EmailListResponse"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "TaskCreateRequest": {"properties": {"task_type": {"type": "string", "title": "Task Type"}, "data": {"additionalProperties": true, "type": "object", "title": "Data"}}, "type": "object", "required": ["task_type", "data"], "title": "TaskCreateRequest"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer"}}}}