#!/usr/bin/env python3
"""
VPN自动注册脚本
简单实现单次注册流程
"""

import asyncio
import json
import random
import string
import time
import logging
from datetime import datetime
import httpx
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import Select

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'vpn_register_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class VPNRegister:
    def __init__(self):
        self.outlook_api_base = "http://localhost:8010"  # OutlookManager API地址
        self.target_url = "https://web2.52pokemon.cc/register"
        
    async def get_available_email(self):
        """从OutlookManager获取可用邮箱"""
        try:
            # 方案1: 直接读取accounts.json文件
            accounts_file = "D:/Dev/Email/OutlookManager/accounts.json"
            with open(accounts_file, 'r', encoding='utf-8') as f:
                accounts = json.load(f)
            
            if accounts:
                email = list(accounts.keys())[0]  # 取第一个邮箱
                logger.info(f"从文件获取到可用邮箱: {email}")
                return email
            else:
                logger.error("accounts.json中没有邮箱账户")
                return None
                
        except Exception as e:
            logger.error(f"读取accounts.json失败: {e}")
            # 方案2: 尝试API（如果文件读取失败）
            try:
                async with httpx.AsyncClient(proxies={}) as client:
                    headers = {"Authorization": "Bearer admin123"}
                    response = await client.get(f"{self.outlook_api_base}/accounts", headers=headers)
                    
                    if response.status_code == 200:
                        accounts = response.json()
                        if accounts:
                            email = accounts[0]['email']
                            logger.info(f"通过API获取到邮箱: {email}")
                            return email
                    else:
                        logger.error(f"API获取邮箱失败: {response.status_code}")
            except Exception as api_error:
                logger.error(f"API调用失败: {api_error}")
            
            return None
    
    def generate_password(self, length=12):
        """生成安全密码"""
        chars = string.ascii_letters + string.digits + "!@#$%"
        password = ''.join(random.choice(chars) for _ in range(length))
        logger.info("生成安全密码")
        return password
    
    async def get_verification_code(self, email, max_wait=60):
        """获取验证码 - 使用正确的API调用方式"""
        logger.info(f"等待验证码邮件: {email}")
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                async with httpx.AsyncClient(proxies={}) as client:
                    headers = {"Authorization": "Bearer admin123"}
                    
                    # 第一步：获取邮件列表（强制刷新）
                    response = await client.get(
                        f"{self.outlook_api_base}/emails/{email}",
                        params={"folder": "inbox", "page": 1, "page_size": 5, "force_refresh": True},
                        headers=headers
                    )
                    
                    if response.status_code == 200:
                        emails_data = response.json()
                        if emails_data.get('emails'):
                            # 遍历最新的邮件
                            for email_item in emails_data['emails']:
                                subject = email_item.get('subject', '')
                                message_id = email_item.get('message_id', '')
                                
                                # 检查邮件主题是否包含验证码相关关键词
                                if any(keyword in subject.lower() for keyword in ['验证码', 'verification', 'code', '宝可梦', 'pokemon']):
                                    logger.info(f"找到可能的验证码邮件: {subject}")
                                    
                                    # 第二步：获取邮件详细内容
                                    detail_response = await client.get(
                                        f"{self.outlook_api_base}/emails/{email}/{message_id}",
                                        headers=headers
                                    )
                                    
                                    if detail_response.status_code == 200:
                                        email_detail = detail_response.json()
                                        body_plain = email_detail.get('body_plain', '')
                                        body_html = email_detail.get('body_html', '')
                                        
                                        # 合并所有文本内容进行搜索
                                        content = f"{subject} {body_plain} {body_html}"
                                        
                                        # 提取验证码（4-6位数字）
                                        import re
                                        code_matches = re.findall(r'\b\d{4,6}\b', content)
                                        if code_matches:
                                            code = code_matches[0]  # 取第一个匹配的验证码
                                            logger.info(f"成功提取验证码: {code}")
                                            return code
                                        else:
                                            logger.warning(f"邮件中未找到验证码: {subject}")
                                    else:
                                        logger.warning(f"获取邮件详情失败: {detail_response.status_code}")
                    else:
                        logger.warning(f"获取邮件列表失败: {response.status_code} - {response.text}")
                
                logger.info("未找到验证码邮件，3秒后重试...")
                await asyncio.sleep(3)  # 等待3秒后重试
                
            except Exception as e:
                logger.error(f"获取验证码时出错: {e}")
                await asyncio.sleep(3)
        
        logger.error("获取验证码超时")
        return None
    
    async def register_with_email(self, email):
        """使用指定邮箱执行注册流程"""
        logger.info(f"使用指定邮箱开始注册流程: {email}")

        # 生成密码
        password = self.generate_password()

        # 执行注册流程
        return await self._execute_registration(email, password)

    async def register_single_account(self):
        """执行单次注册流程"""
        logger.info("开始VPN账户注册流程")

        # 1. 获取邮箱
        email = await self.get_available_email()
        if not email:
            return {"success": False, "error": "无法获取邮箱"}

        # 2. 生成密码
        password = self.generate_password()

        # 3. 执行注册流程
        return await self._execute_registration(email, password)

    async def _execute_registration(self, email, password):
        """执行实际的注册流程"""
        logger.info(f"开始注册流程: {email}")
        
        # 启动浏览器自动化（使用Selenium + Chrome）
        chrome_options = Options()
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        driver = webdriver.Chrome(options=chrome_options)
        
        # 隐藏webdriver特征
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        try:
            # 访问注册页面
            logger.info(f"访问注册页面: {self.target_url}")
            driver.get(self.target_url)
            
            # 等待页面加载
            time.sleep(3)
            current_url = driver.current_url
            logger.info(f"当前页面URL: {current_url}")
            
            # 检查是否被重定向
            if 'login' in current_url or 'register' not in current_url:
                logger.error("页面被重定向，可能被检测到自动化")
                return {"success": False, "error": "页面被反自动化检测重定向"}
            
            # 等待页面元素加载
            wait = WebDriverWait(driver, 10)
            
            # 填写邮箱前缀
            email_prefix = email.split('@')[0]
            logger.info(f"填写邮箱前缀: {email_prefix}")
            
            email_input = wait.until(EC.presence_of_element_located((By.ID, "input-12")))
            email_input.clear()
            email_input.send_keys(email_prefix)
                
                # 选择@outlook.com后缀
                logger.info("选择邮箱后缀: @outlook.com")
                await page.click('.v-select')
                await page.wait_for_timeout(1000)
                await page.click('text=@outlook.com')
                
                # 填写密码
                logger.info("填写密码")
                await page.fill('#input-3', password)
                await page.fill('#input-6', password)  # 确认密码
                
                # 发送验证码
                logger.info("点击发送验证码")
                await page.click('text=发送验证码')
                
                # 等待验证码发送成功的提示或页面变化
                await page.wait_for_timeout(3000)
                logger.info("验证码已发送，开始获取验证码...")
                
                # 获取验证码（等待邮件到达）
                verification_code = await self.get_verification_code(email, max_wait=90)
                if not verification_code:
                    return {"success": False, "error": "获取验证码失败"}
                
                # 填写验证码
                logger.info(f"填写验证码: {verification_code}")
                await page.fill('#input-15', verification_code)
                
                # 等待验证码填写完成
                await page.wait_for_timeout(1000)
                
                # 提交注册
                logger.info("提交注册表单")
                await page.click('text=创建账号')
                
                # 等待注册结果
                await page.wait_for_timeout(5000)
                
                # 检查是否注册成功
                current_url = page.url
                if 'register' not in current_url:
                    logger.info("注册成功！")
                    
                    # 尝试获取订阅链接（这里需要根据实际页面调整）
                    subscription_link = None
                    try:
                        # 这里需要根据实际页面结构来获取订阅链接
                        # subscription_link = await page.locator('text=订阅链接').get_attribute('href')
                        pass
                    except:
                        logger.warning("未能自动获取订阅链接")
                    
                    result = {
                        "success": True,
                        "email": email,
                        "password": password,
                        "subscription_link": subscription_link,
                        "registered_at": datetime.now().isoformat()
                    }
                    
                    # 保存结果
                    with open('vpn_accounts.json', 'a', encoding='utf-8') as f:
                        f.write(json.dumps(result, ensure_ascii=False) + '\n')
                    
                    return result
                else:
                    logger.error("注册失败，仍在注册页面")
                    return {"success": False, "error": "注册失败"}
                
            except Exception as e:
                logger.error(f"注册过程中出错: {e}")
                return {"success": False, "error": str(e)}
            
            finally:
                await browser.close()

async def main():
    """主函数"""
    register = VPNRegister()
    result = await register.register_single_account()
    
    if result["success"]:
        print("注册成功！")
        print(f"邮箱: {result['email']}")
        print(f"密码: {result['password']}")
        if result.get('subscription_link'):
            print(f"订阅链接: {result['subscription_link']}")
    else:
        print(f"注册失败: {result['error']}")

if __name__ == "__main__":
    asyncio.run(main())