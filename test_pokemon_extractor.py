#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pokemon订阅链接提取器测试脚本
用于测试弹窗处理和订阅链接提取功能
"""

import time
import sys
from pokemon_subscription_extractor import PokemonSubscriptionExtractor

def test_popup_handling():
    """测试弹窗处理功能"""
    print("=== 测试弹窗处理功能 ===")
    
    extractor = PokemonSubscriptionExtractor(headless=False)
    
    try:
        # 访问一个可能有弹窗的页面进行测试
        test_url = "https://pokemon.com"  # 替换为实际的测试URL
        extractor.driver.get(test_url)
        print(f"访问测试页面: {test_url}")
        
        # 等待页面加载
        time.sleep(3)
        
        # 测试弹窗处理
        result = extractor.handle_popup()
        if result:
            print("✅ 弹窗处理测试通过")
        else:
            print("❌ 弹窗处理测试失败")
        
        return result
        
    except Exception as e:
        print(f"❌ 弹窗处理测试出错: {e}")
        return False
    finally:
        extractor.close()

def test_subscription_extraction():
    """测试订阅链接提取功能"""
    print("\n=== 测试订阅链接提取功能 ===")
    
    # 注意：这需要有效的登录凭据
    USERNAME = input("请输入用户名 (或按Enter跳过): ").strip()
    if not USERNAME:
        print("跳过订阅链接提取测试（需要登录凭据）")
        return True
    
    PASSWORD = input("请输入密码: ").strip()
    if not PASSWORD:
        print("跳过订阅链接提取测试（需要密码）")
        return True
    
    extractor = PokemonSubscriptionExtractor(headless=False)
    
    try:
        # 运行完整的提取流程
        result = extractor.run(USERNAME, PASSWORD)
        
        if result:
            print("✅ 订阅链接提取测试通过")
            print("提取到的链接:")
            for name, link in result.items():
                print(f"  {name}: {link[:50]}...")
            return True
        else:
            print("❌ 订阅链接提取测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 订阅链接提取测试出错: {e}")
        return False

def test_manual_extraction():
    """手动测试订阅链接提取（假设已经登录）"""
    print("\n=== 手动测试订阅链接提取 ===")
    print("请确保您已经在浏览器中登录到Pokemon网站")
    input("按Enter继续...")
    
    extractor = PokemonSubscriptionExtractor(headless=False)
    
    try:
        # 直接访问仪表板页面
        dashboard_url = input("请输入仪表板URL (或按Enter使用默认): ").strip()
        if not dashboard_url:
            dashboard_url = "https://pokemon.com/dashboard"  # 替换为实际URL
        
        extractor.driver.get(dashboard_url)
        print(f"访问仪表板: {dashboard_url}")
        
        # 等待页面加载
        time.sleep(5)
        
        # 处理弹窗
        extractor.handle_popup()
        
        # 提取订阅链接
        result = extractor.extract_subscription_links()
        
        if result:
            print("✅ 手动测试成功")
            print("提取到的链接:")
            for name, link in result.items():
                print(f"  {name}: {link}")
            return True
        else:
            print("❌ 手动测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 手动测试出错: {e}")
        return False
    finally:
        input("按Enter关闭浏览器...")
        extractor.close()

def main():
    """主测试函数"""
    print("Pokemon订阅链接提取器测试")
    print("=" * 40)
    
    test_options = [
        ("1", "测试弹窗处理功能", test_popup_handling),
        ("2", "测试完整提取流程（需要登录）", test_subscription_extraction),
        ("3", "手动测试（假设已登录）", test_manual_extraction),
        ("4", "运行所有测试", None)
    ]
    
    print("请选择测试选项:")
    for option, description, _ in test_options:
        print(f"  {option}. {description}")
    
    choice = input("\n请输入选项 (1-4): ").strip()
    
    if choice == "1":
        test_popup_handling()
    elif choice == "2":
        test_subscription_extraction()
    elif choice == "3":
        test_manual_extraction()
    elif choice == "4":
        print("运行所有测试...")
        results = []
        results.append(("弹窗处理", test_popup_handling()))
        results.append(("订阅链接提取", test_subscription_extraction()))
        
        print("\n=== 测试结果汇总 ===")
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
    else:
        print("无效选项")
        return

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试过程中出错: {e}")
