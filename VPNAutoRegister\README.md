# VPN自动注册工具

## 功能
- 自动访问 https://web2.52pokemon.cc/register
- 使用OutlookManager中的邮箱进行注册
- 自动获取验证码并完成注册
- 保存注册结果到 vpn_accounts.json

## 使用方法

1. 确保OutlookManager正在运行 (http://localhost:8000)
2. 安装依赖：`pip install -r requirements.txt`
3. 安装浏览器：`playwright install chromium`
4. 运行脚本：`python vpn_register.py`

## 注意事项
- 需要OutlookManager中有可用的邮箱账户
- 脚本会显示浏览器窗口便于调试
- 注册结果保存在 vpn_accounts.json 文件中
- 日志保存在 vpn_register_YYYYMMDD.log 文件中