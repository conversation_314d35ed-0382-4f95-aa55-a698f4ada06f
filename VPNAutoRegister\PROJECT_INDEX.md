# VPN自动注册项目 - 文件索引

## 🎯 项目概述
自动化VPN账户注册系统，使用Patchright绕过反自动化检测，集成OutlookManager进行验证码获取。

## 📁 核心文件结构

### 主要脚本
- **`vpn_register_patchright.py`** - 主要的Patchright自动注册脚本
- **`improved_verification.py`** - 改进的验证码获取逻辑
- **`test_pokemon_verification.py`** - 宝可梦验证码测试脚本
- **`vpn_register.py`** - 原始注册脚本（备用）

### 配置文件
- **`requirements.txt`** - Python依赖包列表
- **`README.md`** - 项目说明文档

### 目录结构
- **`config/`** - 配置文件目录
- **`core/`** - 核心模块目录
- **`utils/`** - 工具函数目录
- **`logs/`** - 日志文件目录
- **`patchright_profile/`** - Patchright浏览器配置文件

## 🔧 技术架构

### 核心技术栈
- **Patchright** - 反检测浏览器自动化
- **OutlookManager API** - 邮件验证码获取
- **Requests** - HTTP API调用
- **正则表达式** - 验证码提取

### 关键功能模块
1. **邮箱管理** - 动态获取可用邮箱账户
2. **验证码获取** - 智能提取最新验证码
3. **表单填写** - 自动化注册表单操作
4. **反检测** - 绕过网站自动化检测

## 🚀 快速启动

### 环境要求
```bash
pip install -r requirements.txt
```

### 主要执行方式
```python
# 完整自动注册
import asyncio
from vpn_register_patchright import VPNRegisterPatchright
asyncio.run(VPNRegisterPatchright().register_single_account())

# 仅验证码获取测试
python test_pokemon_verification.py

# 改进验证码获取
from improved_verification import ImprovedVerificationCodeExtractor
extractor = ImprovedVerificationCodeExtractor()
code = extractor.get_verification_code_for_registration("<EMAIL>")
```

## 📊 项目状态
- **开发阶段**: 基本完成
- **核心功能**: ✅ 已实现
- **测试状态**: ✅ 验证码获取成功
- **部署状态**: 🔄 待优化

## 🔍 关键API端点
- **OutlookManager**: `http://localhost:8010`
- **目标网站**: `https://web2.52pokemon.cc/register`
- **认证**: `Authorization: Bearer admin123`

## 📝 重要说明
1. 需要OutlookManager服务运行在端口8010
2. 验证码邮件通常在垃圾邮件文件夹
3. Patchright配置文件保存在patchright_profile目录
4. 支持6位数字验证码智能提取